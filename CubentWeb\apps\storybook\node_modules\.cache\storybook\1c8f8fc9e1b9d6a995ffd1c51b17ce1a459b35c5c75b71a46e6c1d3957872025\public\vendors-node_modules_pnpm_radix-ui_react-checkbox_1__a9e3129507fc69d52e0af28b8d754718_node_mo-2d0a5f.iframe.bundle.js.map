{"version": 3, "file": "vendors-node_modules_pnpm_radix-ui_react-checkbox_1__a9e3129507fc69d52e0af28b8d754718_node_mo-2d0a5f.iframe.bundle.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAaA;AACA;;;;;;;;;;;;;;;;;AC3RA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;;;;;;;;;;;;;;;;;;;ACfA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;AC1CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;AC7BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;ACpBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;ACbA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "sources": ["webpack://storybook/../../node_modules/.pnpm/@radix-ui+react-checkbox@1._a9e3129507fc69d52e0af28b8d754718/node_modules/@radix-ui/react-checkbox/dist/index.mjs", "webpack://storybook/../../node_modules/.pnpm/@radix-ui+react-use-previou_791288859aab8756064fe392350c2e0c/node_modules/@radix-ui/react-use-previous/dist/index.mjs", "webpack://storybook/../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/Icon.js", "webpack://storybook/../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/createLucideIcon.js", "webpack://storybook/../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/defaultAttributes.js", "webpack://storybook/../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/check.js", "webpack://storybook/../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/shared/src/utils.js"], "sourcesContent": ["\"use client\";\n\n// src/checkbox.tsx\nimport * as React from \"react\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { createContextScope } from \"@radix-ui/react-context\";\nimport { composeEventHandlers } from \"@radix-ui/primitive\";\nimport { useControllableState } from \"@radix-ui/react-use-controllable-state\";\nimport { usePrevious } from \"@radix-ui/react-use-previous\";\nimport { useSize } from \"@radix-ui/react-use-size\";\nimport { Presence } from \"@radix-ui/react-presence\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { Fragment, jsx, jsxs } from \"react/jsx-runtime\";\nvar CHECKBOX_NAME = \"Checkbox\";\nvar [createCheckboxContext, createCheckboxScope] = createContextScope(CHECKBOX_NAME);\nvar [CheckboxProviderImpl, useCheckboxContext] = createCheckboxContext(CHECKBOX_NAME);\nfunction CheckboxProvider(props) {\n  const {\n    __scopeCheckbox,\n    checked: checked<PERSON>rop,\n    children,\n    defaultChecked,\n    disabled,\n    form,\n    name,\n    onCheckedChange,\n    required,\n    value = \"on\",\n    // @ts-expect-error\n    internal_do_not_use_render\n  } = props;\n  const [checked, setChecked] = useControllableState({\n    prop: checkedProp,\n    defaultProp: defaultChecked ?? false,\n    onChange: onCheckedChange,\n    caller: CHECKBOX_NAME\n  });\n  const [control, setControl] = React.useState(null);\n  const [bubbleInput, setBubbleInput] = React.useState(null);\n  const hasConsumerStoppedPropagationRef = React.useRef(false);\n  const isFormControl = control ? !!form || !!control.closest(\"form\") : (\n    // We set this to true by default so that events bubble to forms without JS (SSR)\n    true\n  );\n  const context = {\n    checked,\n    disabled,\n    setChecked,\n    control,\n    setControl,\n    name,\n    form,\n    value,\n    hasConsumerStoppedPropagationRef,\n    required,\n    defaultChecked: isIndeterminate(defaultChecked) ? false : defaultChecked,\n    isFormControl,\n    bubbleInput,\n    setBubbleInput\n  };\n  return /* @__PURE__ */ jsx(\n    CheckboxProviderImpl,\n    {\n      scope: __scopeCheckbox,\n      ...context,\n      children: isFunction(internal_do_not_use_render) ? internal_do_not_use_render(context) : children\n    }\n  );\n}\nvar TRIGGER_NAME = \"CheckboxTrigger\";\nvar CheckboxTrigger = React.forwardRef(\n  ({ __scopeCheckbox, onKeyDown, onClick, ...checkboxProps }, forwardedRef) => {\n    const {\n      control,\n      value,\n      disabled,\n      checked,\n      required,\n      setControl,\n      setChecked,\n      hasConsumerStoppedPropagationRef,\n      isFormControl,\n      bubbleInput\n    } = useCheckboxContext(TRIGGER_NAME, __scopeCheckbox);\n    const composedRefs = useComposedRefs(forwardedRef, setControl);\n    const initialCheckedStateRef = React.useRef(checked);\n    React.useEffect(() => {\n      const form = control?.form;\n      if (form) {\n        const reset = () => setChecked(initialCheckedStateRef.current);\n        form.addEventListener(\"reset\", reset);\n        return () => form.removeEventListener(\"reset\", reset);\n      }\n    }, [control, setChecked]);\n    return /* @__PURE__ */ jsx(\n      Primitive.button,\n      {\n        type: \"button\",\n        role: \"checkbox\",\n        \"aria-checked\": isIndeterminate(checked) ? \"mixed\" : checked,\n        \"aria-required\": required,\n        \"data-state\": getState(checked),\n        \"data-disabled\": disabled ? \"\" : void 0,\n        disabled,\n        value,\n        ...checkboxProps,\n        ref: composedRefs,\n        onKeyDown: composeEventHandlers(onKeyDown, (event) => {\n          if (event.key === \"Enter\") event.preventDefault();\n        }),\n        onClick: composeEventHandlers(onClick, (event) => {\n          setChecked((prevChecked) => isIndeterminate(prevChecked) ? true : !prevChecked);\n          if (bubbleInput && isFormControl) {\n            hasConsumerStoppedPropagationRef.current = event.isPropagationStopped();\n            if (!hasConsumerStoppedPropagationRef.current) event.stopPropagation();\n          }\n        })\n      }\n    );\n  }\n);\nCheckboxTrigger.displayName = TRIGGER_NAME;\nvar Checkbox = React.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      __scopeCheckbox,\n      name,\n      checked,\n      defaultChecked,\n      required,\n      disabled,\n      value,\n      onCheckedChange,\n      form,\n      ...checkboxProps\n    } = props;\n    return /* @__PURE__ */ jsx(\n      CheckboxProvider,\n      {\n        __scopeCheckbox,\n        checked,\n        defaultChecked,\n        disabled,\n        required,\n        onCheckedChange,\n        name,\n        form,\n        value,\n        internal_do_not_use_render: ({ isFormControl }) => /* @__PURE__ */ jsxs(Fragment, { children: [\n          /* @__PURE__ */ jsx(\n            CheckboxTrigger,\n            {\n              ...checkboxProps,\n              ref: forwardedRef,\n              __scopeCheckbox\n            }\n          ),\n          isFormControl && /* @__PURE__ */ jsx(\n            CheckboxBubbleInput,\n            {\n              __scopeCheckbox\n            }\n          )\n        ] })\n      }\n    );\n  }\n);\nCheckbox.displayName = CHECKBOX_NAME;\nvar INDICATOR_NAME = \"CheckboxIndicator\";\nvar CheckboxIndicator = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeCheckbox, forceMount, ...indicatorProps } = props;\n    const context = useCheckboxContext(INDICATOR_NAME, __scopeCheckbox);\n    return /* @__PURE__ */ jsx(\n      Presence,\n      {\n        present: forceMount || isIndeterminate(context.checked) || context.checked === true,\n        children: /* @__PURE__ */ jsx(\n          Primitive.span,\n          {\n            \"data-state\": getState(context.checked),\n            \"data-disabled\": context.disabled ? \"\" : void 0,\n            ...indicatorProps,\n            ref: forwardedRef,\n            style: { pointerEvents: \"none\", ...props.style }\n          }\n        )\n      }\n    );\n  }\n);\nCheckboxIndicator.displayName = INDICATOR_NAME;\nvar BUBBLE_INPUT_NAME = \"CheckboxBubbleInput\";\nvar CheckboxBubbleInput = React.forwardRef(\n  ({ __scopeCheckbox, ...props }, forwardedRef) => {\n    const {\n      control,\n      hasConsumerStoppedPropagationRef,\n      checked,\n      defaultChecked,\n      required,\n      disabled,\n      name,\n      value,\n      form,\n      bubbleInput,\n      setBubbleInput\n    } = useCheckboxContext(BUBBLE_INPUT_NAME, __scopeCheckbox);\n    const composedRefs = useComposedRefs(forwardedRef, setBubbleInput);\n    const prevChecked = usePrevious(checked);\n    const controlSize = useSize(control);\n    React.useEffect(() => {\n      const input = bubbleInput;\n      if (!input) return;\n      const inputProto = window.HTMLInputElement.prototype;\n      const descriptor = Object.getOwnPropertyDescriptor(\n        inputProto,\n        \"checked\"\n      );\n      const setChecked = descriptor.set;\n      const bubbles = !hasConsumerStoppedPropagationRef.current;\n      if (prevChecked !== checked && setChecked) {\n        const event = new Event(\"click\", { bubbles });\n        input.indeterminate = isIndeterminate(checked);\n        setChecked.call(input, isIndeterminate(checked) ? false : checked);\n        input.dispatchEvent(event);\n      }\n    }, [bubbleInput, prevChecked, checked, hasConsumerStoppedPropagationRef]);\n    const defaultCheckedRef = React.useRef(isIndeterminate(checked) ? false : checked);\n    return /* @__PURE__ */ jsx(\n      Primitive.input,\n      {\n        type: \"checkbox\",\n        \"aria-hidden\": true,\n        defaultChecked: defaultChecked ?? defaultCheckedRef.current,\n        required,\n        disabled,\n        name,\n        value,\n        form,\n        ...props,\n        tabIndex: -1,\n        ref: composedRefs,\n        style: {\n          ...props.style,\n          ...controlSize,\n          position: \"absolute\",\n          pointerEvents: \"none\",\n          opacity: 0,\n          margin: 0,\n          // We transform because the input is absolutely positioned but we have\n          // rendered it **after** the button. This pulls it back to sit on top\n          // of the button.\n          transform: \"translateX(-100%)\"\n        }\n      }\n    );\n  }\n);\nCheckboxBubbleInput.displayName = BUBBLE_INPUT_NAME;\nfunction isFunction(value) {\n  return typeof value === \"function\";\n}\nfunction isIndeterminate(checked) {\n  return checked === \"indeterminate\";\n}\nfunction getState(checked) {\n  return isIndeterminate(checked) ? \"indeterminate\" : checked ? \"checked\" : \"unchecked\";\n}\nexport {\n  Checkbox,\n  CheckboxIndicator,\n  CheckboxIndicator as Indicator,\n  Checkbox as Root,\n  createCheckboxScope,\n  CheckboxBubbleInput as unstable_BubbleInput,\n  CheckboxBubbleInput as unstable_CheckboxBubbleInput,\n  CheckboxProvider as unstable_CheckboxProvider,\n  CheckboxTrigger as unstable_CheckboxTrigger,\n  CheckboxProvider as unstable_Provider,\n  CheckboxTrigger as unstable_Trigger\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/use-previous/src/use-previous.tsx\nimport * as React from \"react\";\nfunction usePrevious(value) {\n  const ref = React.useRef({ value, previous: value });\n  return React.useMemo(() => {\n    if (ref.current.value !== value) {\n      ref.current.previous = ref.current.value;\n      ref.current.value = value;\n    }\n    return ref.current.previous;\n  }, [value]);\n}\nexport {\n  usePrevious\n};\n//# sourceMappingURL=index.mjs.map\n", "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport { forwardRef, createElement } from 'react';\nimport defaultAttributes from './defaultAttributes.js';\nimport { mergeClasses, hasA11yProp } from './shared/src/utils.js';\n\nconst Icon = forwardRef(\n  ({\n    color = \"currentColor\",\n    size = 24,\n    strokeWidth = 2,\n    absoluteStrokeWidth,\n    className = \"\",\n    children,\n    iconNode,\n    ...rest\n  }, ref) => createElement(\n    \"svg\",\n    {\n      ref,\n      ...defaultAttributes,\n      width: size,\n      height: size,\n      stroke: color,\n      strokeWidth: absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,\n      className: mergeClasses(\"lucide\", className),\n      ...!children && !hasA11yProp(rest) && { \"aria-hidden\": \"true\" },\n      ...rest\n    },\n    [\n      ...iconNode.map(([tag, attrs]) => createElement(tag, attrs)),\n      ...Array.isArray(children) ? children : [children]\n    ]\n  )\n);\n\nexport { Icon as default };\n//# sourceMappingURL=Icon.js.map\n", "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport { forwardRef, createElement } from 'react';\nimport { mergeClasses, toKebabCase, toPascalCase } from './shared/src/utils.js';\nimport Icon from './Icon.js';\n\nconst createLucideIcon = (iconName, iconNode) => {\n  const Component = forwardRef(\n    ({ className, ...props }, ref) => createElement(Icon, {\n      ref,\n      iconNode,\n      className: mergeClasses(\n        `lucide-${toKebabCase(toPascalCase(iconName))}`,\n        `lucide-${iconName}`,\n        className\n      ),\n      ...props\n    })\n  );\n  Component.displayName = toPascalCase(iconName);\n  return Component;\n};\n\nexport { createLucideIcon as default };\n//# sourceMappingURL=createLucideIcon.js.map\n", "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nvar defaultAttributes = {\n  xmlns: \"http://www.w3.org/2000/svg\",\n  width: 24,\n  height: 24,\n  viewBox: \"0 0 24 24\",\n  fill: \"none\",\n  stroke: \"currentColor\",\n  strokeWidth: 2,\n  strokeLinecap: \"round\",\n  strokeLinejoin: \"round\"\n};\n\nexport { defaultAttributes as default };\n//# sourceMappingURL=defaultAttributes.js.map\n", "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [[\"path\", { d: \"M20 6 9 17l-5-5\", key: \"1gmf2c\" }]];\nconst Check = createLucideIcon(\"check\", __iconNode);\n\nexport { __iconNode, Check as default };\n//# sourceMappingURL=check.js.map\n", "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nconst toKebabCase = (string) => string.replace(/([a-z0-9])([A-Z])/g, \"$1-$2\").toLowerCase();\nconst toCamelCase = (string) => string.replace(\n  /^([A-Z])|[\\s-_]+(\\w)/g,\n  (match, p1, p2) => p2 ? p2.toUpperCase() : p1.toLowerCase()\n);\nconst toPascalCase = (string) => {\n  const camelCase = toCamelCase(string);\n  return camelCase.charAt(0).toUpperCase() + camelCase.slice(1);\n};\nconst mergeClasses = (...classes) => classes.filter((className, index, array) => {\n  return Boolean(className) && className.trim() !== \"\" && array.indexOf(className) === index;\n}).join(\" \").trim();\nconst hasA11yProp = (props) => {\n  for (const prop in props) {\n    if (prop.startsWith(\"aria-\") || prop === \"role\" || prop === \"title\") {\n      return true;\n    }\n  }\n};\n\nexport { hasA11yProp, mergeClasses, toCamelCase, toKebabCase, toPascalCase };\n//# sourceMappingURL=utils.js.map\n"], "names": [], "sourceRoot": ""}