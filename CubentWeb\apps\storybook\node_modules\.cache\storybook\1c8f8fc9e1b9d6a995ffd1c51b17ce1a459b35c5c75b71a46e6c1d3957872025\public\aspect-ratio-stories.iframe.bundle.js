"use strict";
(self["webpackChunkstorybook"] = self["webpackChunkstorybook"] || []).push([["aspect-ratio-stories"],{

/***/ "../../packages/design-system/components/ui/aspect-ratio.tsx":
/*!*******************************************************************!*\
  !*** ../../packages/design-system/components/ui/aspect-ratio.tsx ***!
  \*******************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AspectRatio: () => (/* binding */ AspectRatio)
/* harmony export */ });
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/jsx-dev-runtime.js");
/* harmony import */ var _radix_ui_react_aspect_ratio__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-aspect-ratio */ "../../node_modules/.pnpm/@radix-ui+react-aspect-rati_b1df01c60c5202d279975a645871fde9/node_modules/@radix-ui/react-aspect-ratio/dist/index.mjs");
/* provided dependency */ var __react_refresh_utils__ = __webpack_require__(/*! ../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js */ "../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js");
__webpack_require__.$Refresh$.runtime = __webpack_require__(/*! ../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js */ "../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js");

"use client";


function AspectRatio({ ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_aspect_ratio__WEBPACK_IMPORTED_MODULE_1__.Root, {
        "data-slot": "aspect-ratio",
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\packages\\design-system\\components\\ui\\aspect-ratio.tsx",
        lineNumber: 8,
        columnNumber: 10
    }, this);
}
_c = AspectRatio;

AspectRatio.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "AspectRatio"
};
var _c;
__webpack_require__.$Refresh$.register(_c, "AspectRatio");


const $ReactRefreshModuleId$ = __webpack_require__.$Refresh$.moduleId;
const $ReactRefreshCurrentExports$ = __react_refresh_utils__.getModuleExports(
	$ReactRefreshModuleId$
);

function $ReactRefreshModuleRuntime$(exports) {
	if (true) {
		let errorOverlay;
		if (true) {
			errorOverlay = false;
		}
		let testMode;
		if (typeof __react_refresh_test__ !== 'undefined') {
			testMode = __react_refresh_test__;
		}
		return __react_refresh_utils__.executeRuntime(
			exports,
			$ReactRefreshModuleId$,
			module.hot,
			errorOverlay,
			testMode
		);
	}
}

if (typeof Promise !== 'undefined' && $ReactRefreshCurrentExports$ instanceof Promise) {
	$ReactRefreshCurrentExports$.then($ReactRefreshModuleRuntime$);
} else {
	$ReactRefreshModuleRuntime$($ReactRefreshCurrentExports$);
}

/***/ }),

/***/ "./stories/aspect-ratio.stories.tsx":
/*!******************************************!*\
  !*** ./stories/aspect-ratio.stories.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Cinemascope: () => (/* binding */ Cinemascope),
/* harmony export */   Default: () => (/* binding */ Default),
/* harmony export */   Landscape: () => (/* binding */ Landscape),
/* harmony export */   Square: () => (/* binding */ Square),
/* harmony export */   __namedExportsOrder: () => (/* binding */ __namedExportsOrder),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/jsx-dev-runtime.js");
/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ "../../node_modules/.pnpm/@storybook+nextjs@8.6.14_es_ebe272ef4db6a93de59db9f60d79f59e/node_modules/@storybook/nextjs/dist/images/next-image.mjs");
/* harmony import */ var _repo_design_system_components_ui_aspect_ratio__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @repo/design-system/components/ui/aspect-ratio */ "../../packages/design-system/components/ui/aspect-ratio.tsx");
/* provided dependency */ var __react_refresh_utils__ = __webpack_require__(/*! ../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js */ "../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js");
__webpack_require__.$Refresh$.runtime = __webpack_require__(/*! ../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js */ "../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js");




/**
 * Displays content within a desired ratio.
 */
const meta = {
  title: 'ui/AspectRatio',
  component: _repo_design_system_components_ui_aspect_ratio__WEBPACK_IMPORTED_MODULE_2__.AspectRatio,
  tags: ['autodocs'],
  argTypes: {},
  render: args => /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_aspect_ratio__WEBPACK_IMPORTED_MODULE_2__.AspectRatio, {
    ...args,
    className: "bg-slate-50 dark:bg-slate-800",
    children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__["default"], {
      src: "https://images.unsplash.com/photo-1576075796033-848c2a5f3696?w=800&dpr=2&q=80",
      alt: "Photo by Alvaro Pinot",
      fill: true,
      className: "rounded-md object-cover"
    }, void 0, false, {
      fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\aspect-ratio.stories.tsx",
      lineNumber: 16,
      columnNumber: 7
    }, undefined)
  }, void 0, false, {
    fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\aspect-ratio.stories.tsx",
    lineNumber: 15,
    columnNumber: 5
  }, undefined),
  decorators: [Story => /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
    className: "w-1/2",
    children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Story, {}, void 0, false, {
      fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\aspect-ratio.stories.tsx",
      lineNumber: 27,
      columnNumber: 9
    }, undefined)
  }, void 0, false, {
    fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\aspect-ratio.stories.tsx",
    lineNumber: 26,
    columnNumber: 7
  }, undefined)],
  parameters: {
    docs: {
      description: {
        component: "Displays content within a desired ratio."
      }
    }
  }
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (meta);
/**
 * The default form of the aspect ratio.
 */
const Default = {
  args: {
    ratio: 16 / 9
  }
};
/**
 * Use the `1:1` aspect ratio to display a square image.
 */
const Square = {
  args: {
    ratio: 1
  }
};
/**
 * Use the `4:3` aspect ratio to display a landscape image.
 */
const Landscape = {
  args: {
    ratio: 4 / 3
  }
};
/**
 * Use the `2.35:1` aspect ratio to display a cinemascope image.
 */
const Cinemascope = {
  args: {
    ratio: 2.35 / 1
  }
};
;
const __namedExportsOrder = ["Default", "Square", "Landscape", "Cinemascope"];
Default.parameters = {
  ...Default.parameters,
  docs: {
    ...Default.parameters?.docs,
    source: {
      originalSource: "{\n  args: {\n    ratio: 16 / 9\n  }\n}",
      ...Default.parameters?.docs?.source
    },
    description: {
      story: "The default form of the aspect ratio.",
      ...Default.parameters?.docs?.description
    }
  }
};
Square.parameters = {
  ...Square.parameters,
  docs: {
    ...Square.parameters?.docs,
    source: {
      originalSource: "{\n  args: {\n    ratio: 1\n  }\n}",
      ...Square.parameters?.docs?.source
    },
    description: {
      story: "Use the `1:1` aspect ratio to display a square image.",
      ...Square.parameters?.docs?.description
    }
  }
};
Landscape.parameters = {
  ...Landscape.parameters,
  docs: {
    ...Landscape.parameters?.docs,
    source: {
      originalSource: "{\n  args: {\n    ratio: 4 / 3\n  }\n}",
      ...Landscape.parameters?.docs?.source
    },
    description: {
      story: "Use the `4:3` aspect ratio to display a landscape image.",
      ...Landscape.parameters?.docs?.description
    }
  }
};
Cinemascope.parameters = {
  ...Cinemascope.parameters,
  docs: {
    ...Cinemascope.parameters?.docs,
    source: {
      originalSource: "{\n  args: {\n    ratio: 2.35 / 1\n  }\n}",
      ...Cinemascope.parameters?.docs?.source
    },
    description: {
      story: "Use the `2.35:1` aspect ratio to display a cinemascope image.",
      ...Cinemascope.parameters?.docs?.description
    }
  }
};

const $ReactRefreshModuleId$ = __webpack_require__.$Refresh$.moduleId;
const $ReactRefreshCurrentExports$ = __react_refresh_utils__.getModuleExports(
	$ReactRefreshModuleId$
);

function $ReactRefreshModuleRuntime$(exports) {
	if (true) {
		let errorOverlay;
		if (true) {
			errorOverlay = false;
		}
		let testMode;
		if (typeof __react_refresh_test__ !== 'undefined') {
			testMode = __react_refresh_test__;
		}
		return __react_refresh_utils__.executeRuntime(
			exports,
			$ReactRefreshModuleId$,
			module.hot,
			errorOverlay,
			testMode
		);
	}
}

if (typeof Promise !== 'undefined' && $ReactRefreshCurrentExports$ instanceof Promise) {
	$ReactRefreshCurrentExports$.then($ReactRefreshModuleRuntime$);
} else {
	$ReactRefreshModuleRuntime$($ReactRefreshCurrentExports$);
}

/***/ })

}]);
//# sourceMappingURL=aspect-ratio-stories.iframe.bundle.js.map