"use strict";
(self["webpackChunkstorybook"] = self["webpackChunkstorybook"] || []).push([["node_modules_pnpm_next_15_3_2__babel_core_7_2_09efc9edc30971f6ad8d5c21a0ab8f1f_node_modules_n-1d918a"],{

/***/ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react-dom/cjs/react-dom-test-utils.production.js":
/*!******************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react-dom/cjs/react-dom-test-utils.production.js ***!
  \******************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

/* provided dependency */ var console = __webpack_require__(/*! ../../node_modules/.pnpm/console-browserify@1.2.0/node_modules/console-browserify/index.js */ "../../node_modules/.pnpm/console-browserify@1.2.0/node_modules/console-browserify/index.js");
/**
 * @license React
 * react-dom-test-utils.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */


var React = __webpack_require__(/*! next/dist/compiled/react */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js"),
  didWarnAboutUsingAct = !1;
exports.act = function (callback) {
  !1 === didWarnAboutUsingAct &&
    ((didWarnAboutUsingAct = !0),
    console.error(
      "`ReactDOMTestUtils.act` is deprecated in favor of `React.act`. Import `act` from `react` instead of `react-dom/test-utils`. See https://react.dev/warnings/react-dom-test-utils for more info."
    ));
  return React.act(callback);
};


/***/ })

}]);
//# sourceMappingURL=node_modules_pnpm_next_15_3_2__babel_core_7_2_09efc9edc30971f6ad8d5c21a0ab8f1f_node_modules_n-1d918a.iframe.bundle.js.map