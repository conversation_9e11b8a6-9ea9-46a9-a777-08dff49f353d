{"version": 3, "file": "input-stories.iframe.bundle.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;AAEA;AAEA;AACA;AAEA;AACA;AACA;AAMA;;;;;;AAGA;AAdA;AAgBA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClBA;AAEA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AAIA;;;AAGA;AAEA;;;;AAIA;AACA;AAAA;AAAA;AACA;AAEA;;;;AAIA;AACA;AACA;AACA;AAAA;AAAA;;;;;AACA;AAAA;AAAA;;;;;;;;;;;AAGA;AAEA;;;;AAIA;AACA;AACA;AACA;AAAA;AAAA;;;;;AACA;AAAA;AAAA;;;;;AACA;AAAA;AAAA;;;;;;;;;;;AAGA;AAEA;;;;AAIA;AACA;AACA;AACA;AAAA;;;;;AACA;AACA;AACA;AACA;;;;;;;;;;;AAKA", "sources": ["webpack://storybook/../../packages/design-system/components/ui/input.tsx", "webpack://storybook/./stories/input.stories.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@repo/design-system/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n", "import type { <PERSON>a, StoryObj } from '@storybook/react';\n\nimport { Input } from '@repo/design-system/components/ui/input';\n\n/**\n * Displays a form input field or a component that looks like an input field.\n */\nconst meta = {\n  title: 'ui/Input',\n  component: Input,\n  tags: ['autodocs'],\n  argTypes: {},\n  args: {\n    className: 'w-96',\n    type: 'email',\n    placeholder: 'Email',\n    disabled: false,\n  },\n  parameters: {\n    layout: 'centered',\n  },\n} satisfies Meta<typeof Input>;\n\nexport default meta;\n\ntype Story = StoryObj<typeof meta>;\n\n/**\n * The default form of the input field.\n */\nexport const Default: Story = {};\n\n/**\n * Use the `disabled` prop to make the input non-interactive and appears faded,\n * indicating that input is not currently accepted.\n */\nexport const Disabled: Story = {\n  args: { disabled: true },\n};\n\n/**\n * Use the `Label` component to includes a clear, descriptive label above or\n * alongside the input area to guide users.\n */\nexport const WithLabel: Story = {\n  render: (args) => (\n    <div className=\"grid items-center gap-1.5\">\n      <label htmlFor=\"email\">{args.placeholder}</label>\n      <Input {...args} id=\"email\" />\n    </div>\n  ),\n};\n\n/**\n * Use a text element below the input field to provide additional instructions\n * or information to users.\n */\nexport const WithHelperText: Story = {\n  render: (args) => (\n    <div className=\"grid items-center gap-1.5\">\n      <label htmlFor=\"email-2\">{args.placeholder}</label>\n      <Input {...args} id=\"email-2\" />\n      <p className=\"text-foreground/50 text-sm\">Enter your email address.</p>\n    </div>\n  ),\n};\n\n/**\n * Use the `Button` component to indicate that the input field can be submitted\n * or used to trigger an action.\n */\nexport const WithButton: Story = {\n  render: (args) => (\n    <div className=\"flex items-center space-x-2\">\n      <Input {...args} />\n      <button\n        className=\"rounded bg-primary px-4 py-2 text-primary-foreground\"\n        type=\"submit\"\n      >\n        Subscribe\n      </button>\n    </div>\n  ),\n};\n"], "names": [], "sourceRoot": ""}