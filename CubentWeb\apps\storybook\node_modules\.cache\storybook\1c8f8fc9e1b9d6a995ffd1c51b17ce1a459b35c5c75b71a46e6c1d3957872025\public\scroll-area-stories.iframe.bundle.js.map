{"version": 3, "file": "scroll-area-stories.iframe.bundle.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA;;AAEA;AACA;AAEA;AAEA;AAKA;AAEA;AACA;AACA;;AAEA;AACA;AACA;AAEA;;;;;;AAEA;;;;;AACA;;;;;;;;;;;AAGA;AArBA;AAuBA;AAKA;AAEA;AACA;AACA;AAQA;AAEA;AACA;AACA;;;;;;;;;;;AAIA;AAzBA;AA2BA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvDA;AAEA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AAIA;;;AAGA;AAEA;;;AAGA;AACA;AACA;AACA;AACA;AAEA;;;AAGA;AACA;AACA;AACA;AACA;AAEA;;;AAGA;AACA;AACA;AACA;AACA", "sources": ["webpack://storybook/../../packages/design-system/components/ui/scroll-area.tsx", "webpack://storybook/./stories/scroll-area.stories.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ScrollAreaPrimitive from \"@radix-ui/react-scroll-area\"\n\nimport { cn } from \"@repo/design-system/lib/utils\"\n\nfunction ScrollArea({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof ScrollAreaPrimitive.Root>) {\n  return (\n    <ScrollAreaPrimitive.Root\n      data-slot=\"scroll-area\"\n      className={cn(\"relative\", className)}\n      {...props}\n    >\n      <ScrollAreaPrimitive.Viewport\n        data-slot=\"scroll-area-viewport\"\n        className=\"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1\"\n      >\n        {children}\n      </ScrollAreaPrimitive.Viewport>\n      <ScrollBar />\n      <ScrollAreaPrimitive.Corner />\n    </ScrollAreaPrimitive.Root>\n  )\n}\n\nfunction ScrollBar({\n  className,\n  orientation = \"vertical\",\n  ...props\n}: React.ComponentProps<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>) {\n  return (\n    <ScrollAreaPrimitive.ScrollAreaScrollbar\n      data-slot=\"scroll-area-scrollbar\"\n      orientation={orientation}\n      className={cn(\n        \"flex touch-none p-px transition-colors select-none\",\n        orientation === \"vertical\" &&\n          \"h-full w-2.5 border-l border-l-transparent\",\n        orientation === \"horizontal\" &&\n          \"h-2.5 flex-col border-t border-t-transparent\",\n        className\n      )}\n      {...props}\n    >\n      <ScrollAreaPrimitive.ScrollAreaThumb\n        data-slot=\"scroll-area-thumb\"\n        className=\"bg-border relative flex-1 rounded-full\"\n      />\n    </ScrollAreaPrimitive.ScrollAreaScrollbar>\n  )\n}\n\nexport { ScrollArea, ScrollBar }\n", "import type { Meta, StoryObj } from '@storybook/react';\n\nimport { ScrollArea } from '@repo/design-system/components/ui/scroll-area';\n\n/**\n * Augments native scroll functionality for custom, cross-browser styling.\n */\nconst meta = {\n  title: 'ui/ScrollArea',\n  component: ScrollArea,\n  tags: ['autodocs'],\n  argTypes: {\n    children: {\n      control: 'text',\n    },\n  },\n  args: {\n    className: 'h-32 w-80 rounded-md border p-4',\n    type: 'auto',\n    children:\n      \"<PERSON><PERSON><PERSON> began sneaking into the castle in the middle of the night and leaving jokes all over the place: under the king's pillow, in his soup, even in the royal toilet. The king was furious, but he couldn't seem to stop <PERSON><PERSON><PERSON>. And then, one day, the people of the kingdom discovered that the jokes left by <PERSON><PERSON><PERSON> were so funny that they couldn't help but laugh. And once they started laughing, they couldn't stop. The king was so angry that he banished Jo<PERSON><PERSON> from the kingdom, but the people still laughed, and they laughed, and they laughed. And they all lived happily ever after.\",\n  },\n  parameters: {\n    layout: 'centered',\n  },\n} satisfies Meta<typeof ScrollArea>;\n\nexport default meta;\n\ntype Story = StoryObj<typeof meta>;\n\n/**\n * The default form of the scroll area.\n */\nexport const Default: Story = {};\n\n/**\n * Use the `type` prop with `always` to always show the scroll area.\n */\nexport const Always: Story = {\n  args: {\n    type: 'always',\n  },\n};\n\n/**\n * Use the `type` prop with `hover` to show the scroll area on hover.\n */\nexport const Hover: Story = {\n  args: {\n    type: 'hover',\n  },\n};\n\n/**\n * Use the `type` prop with `scroll` to show the scroll area when scrolling.\n */\nexport const Scroll: Story = {\n  args: {\n    type: 'scroll',\n  },\n};\n"], "names": [], "sourceRoot": ""}