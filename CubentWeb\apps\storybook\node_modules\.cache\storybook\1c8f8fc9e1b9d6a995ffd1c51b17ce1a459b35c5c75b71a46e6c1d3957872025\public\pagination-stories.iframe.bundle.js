"use strict";
(self["webpackChunkstorybook"] = self["webpackChunkstorybook"] || []).push([["pagination-stories"],{

/***/ "../../node_modules/.pnpm/class-variance-authority@0.7.1/node_modules/class-variance-authority/dist/index.mjs":
/*!********************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/class-variance-authority@0.7.1/node_modules/class-variance-authority/dist/index.mjs ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   cva: () => (/* binding */ cva),
/* harmony export */   cx: () => (/* binding */ cx)
/* harmony export */ });
/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ "../../node_modules/.pnpm/clsx@2.1.1/node_modules/clsx/dist/clsx.mjs");
/**
 * Copyright 2022 Joe Bell. All rights reserved.
 *
 * This file is licensed to you under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with the
 * License. You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR REPRESENTATIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */ 
const falsyToString = (value)=>typeof value === "boolean" ? `${value}` : value === 0 ? "0" : value;
const cx = clsx__WEBPACK_IMPORTED_MODULE_0__.clsx;
const cva = (base, config)=>(props)=>{
        var _config_compoundVariants;
        if ((config === null || config === void 0 ? void 0 : config.variants) == null) return cx(base, props === null || props === void 0 ? void 0 : props.class, props === null || props === void 0 ? void 0 : props.className);
        const { variants, defaultVariants } = config;
        const getVariantClassNames = Object.keys(variants).map((variant)=>{
            const variantProp = props === null || props === void 0 ? void 0 : props[variant];
            const defaultVariantProp = defaultVariants === null || defaultVariants === void 0 ? void 0 : defaultVariants[variant];
            if (variantProp === null) return null;
            const variantKey = falsyToString(variantProp) || falsyToString(defaultVariantProp);
            return variants[variant][variantKey];
        });
        const propsWithoutUndefined = props && Object.entries(props).reduce((acc, param)=>{
            let [key, value] = param;
            if (value === undefined) {
                return acc;
            }
            acc[key] = value;
            return acc;
        }, {});
        const getCompoundVariantClassNames = config === null || config === void 0 ? void 0 : (_config_compoundVariants = config.compoundVariants) === null || _config_compoundVariants === void 0 ? void 0 : _config_compoundVariants.reduce((acc, param)=>{
            let { class: cvClass, className: cvClassName, ...compoundVariantOptions } = param;
            return Object.entries(compoundVariantOptions).every((param)=>{
                let [key, value] = param;
                return Array.isArray(value) ? value.includes({
                    ...defaultVariants,
                    ...propsWithoutUndefined
                }[key]) : ({
                    ...defaultVariants,
                    ...propsWithoutUndefined
                })[key] === value;
            }) ? [
                ...acc,
                cvClass,
                cvClassName
            ] : acc;
        }, []);
        return cx(base, getVariantClassNames, getCompoundVariantClassNames, props === null || props === void 0 ? void 0 : props.class, props === null || props === void 0 ? void 0 : props.className);
    };



/***/ }),

/***/ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/Icon.js":
/*!*************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/Icon.js ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ Icon)
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _defaultAttributes_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./defaultAttributes.js */ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/defaultAttributes.js");
/* harmony import */ var _shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./shared/src/utils.js */ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/shared/src/utils.js");
/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */





const Icon = (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)(
  ({
    color = "currentColor",
    size = 24,
    strokeWidth = 2,
    absoluteStrokeWidth,
    className = "",
    children,
    iconNode,
    ...rest
  }, ref) => (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(
    "svg",
    {
      ref,
      ..._defaultAttributes_js__WEBPACK_IMPORTED_MODULE_1__["default"],
      width: size,
      height: size,
      stroke: color,
      strokeWidth: absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,
      className: (0,_shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__.mergeClasses)("lucide", className),
      ...!children && !(0,_shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__.hasA11yProp)(rest) && { "aria-hidden": "true" },
      ...rest
    },
    [
      ...iconNode.map(([tag, attrs]) => (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(tag, attrs)),
      ...Array.isArray(children) ? children : [children]
    ]
  )
);


//# sourceMappingURL=Icon.js.map


/***/ }),

/***/ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/createLucideIcon.js":
/*!*************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/createLucideIcon.js ***!
  \*************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ createLucideIcon)
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./shared/src/utils.js */ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/shared/src/utils.js");
/* harmony import */ var _Icon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Icon.js */ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/Icon.js");
/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */





const createLucideIcon = (iconName, iconNode) => {
  const Component = (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)(
    ({ className, ...props }, ref) => (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(_Icon_js__WEBPACK_IMPORTED_MODULE_1__["default"], {
      ref,
      iconNode,
      className: (0,_shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__.mergeClasses)(
        `lucide-${(0,_shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__.toKebabCase)((0,_shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__.toPascalCase)(iconName))}`,
        `lucide-${iconName}`,
        className
      ),
      ...props
    })
  );
  Component.displayName = (0,_shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__.toPascalCase)(iconName);
  return Component;
};


//# sourceMappingURL=createLucideIcon.js.map


/***/ }),

/***/ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/defaultAttributes.js":
/*!**************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/defaultAttributes.js ***!
  \**************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ defaultAttributes)
/* harmony export */ });
/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

var defaultAttributes = {
  xmlns: "http://www.w3.org/2000/svg",
  width: 24,
  height: 24,
  viewBox: "0 0 24 24",
  fill: "none",
  stroke: "currentColor",
  strokeWidth: 2,
  strokeLinecap: "round",
  strokeLinejoin: "round"
};


//# sourceMappingURL=defaultAttributes.js.map


/***/ }),

/***/ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/chevron-left.js":
/*!***************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/chevron-left.js ***!
  \***************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   __iconNode: () => (/* binding */ __iconNode),
/* harmony export */   "default": () => (/* binding */ ChevronLeft)
/* harmony export */ });
/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/createLucideIcon.js");
/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */



const __iconNode = [["path", { d: "m15 18-6-6 6-6", key: "1wnfg3" }]];
const ChevronLeft = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__["default"])("chevron-left", __iconNode);


//# sourceMappingURL=chevron-left.js.map


/***/ }),

/***/ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/chevron-right.js":
/*!****************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/chevron-right.js ***!
  \****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   __iconNode: () => (/* binding */ __iconNode),
/* harmony export */   "default": () => (/* binding */ ChevronRight)
/* harmony export */ });
/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/createLucideIcon.js");
/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */



const __iconNode = [["path", { d: "m9 18 6-6-6-6", key: "mthhwq" }]];
const ChevronRight = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__["default"])("chevron-right", __iconNode);


//# sourceMappingURL=chevron-right.js.map


/***/ }),

/***/ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/ellipsis.js":
/*!***********************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/ellipsis.js ***!
  \***********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   __iconNode: () => (/* binding */ __iconNode),
/* harmony export */   "default": () => (/* binding */ Ellipsis)
/* harmony export */ });
/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/createLucideIcon.js");
/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */



const __iconNode = [
  ["circle", { cx: "12", cy: "12", r: "1", key: "41hilf" }],
  ["circle", { cx: "19", cy: "12", r: "1", key: "1wjl8i" }],
  ["circle", { cx: "5", cy: "12", r: "1", key: "1pcz8c" }]
];
const Ellipsis = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__["default"])("ellipsis", __iconNode);


//# sourceMappingURL=ellipsis.js.map


/***/ }),

/***/ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/shared/src/utils.js":
/*!*************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/shared/src/utils.js ***!
  \*************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   hasA11yProp: () => (/* binding */ hasA11yProp),
/* harmony export */   mergeClasses: () => (/* binding */ mergeClasses),
/* harmony export */   toCamelCase: () => (/* binding */ toCamelCase),
/* harmony export */   toKebabCase: () => (/* binding */ toKebabCase),
/* harmony export */   toPascalCase: () => (/* binding */ toPascalCase)
/* harmony export */ });
/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

const toKebabCase = (string) => string.replace(/([a-z0-9])([A-Z])/g, "$1-$2").toLowerCase();
const toCamelCase = (string) => string.replace(
  /^([A-Z])|[\s-_]+(\w)/g,
  (match, p1, p2) => p2 ? p2.toUpperCase() : p1.toLowerCase()
);
const toPascalCase = (string) => {
  const camelCase = toCamelCase(string);
  return camelCase.charAt(0).toUpperCase() + camelCase.slice(1);
};
const mergeClasses = (...classes) => classes.filter((className, index, array) => {
  return Boolean(className) && className.trim() !== "" && array.indexOf(className) === index;
}).join(" ").trim();
const hasA11yProp = (props) => {
  for (const prop in props) {
    if (prop.startsWith("aria-") || prop === "role" || prop === "title") {
      return true;
    }
  }
};


//# sourceMappingURL=utils.js.map


/***/ }),

/***/ "../../packages/design-system/components/ui/button.tsx":
/*!*************************************************************!*\
  !*** ../../packages/design-system/components/ui/button.tsx ***!
  \*************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Button: () => (/* binding */ Button),
/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)
/* harmony export */ });
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/jsx-dev-runtime.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ "../../node_modules/.pnpm/@radix-ui+react-slot@1.2.3_@types+react@19.1.5_react@19.1.0/node_modules/@radix-ui/react-slot/dist/index.mjs");
/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! class-variance-authority */ "../../node_modules/.pnpm/class-variance-authority@0.7.1/node_modules/class-variance-authority/dist/index.mjs");
/* harmony import */ var _repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @repo/design-system/lib/utils */ "../../packages/design-system/lib/utils.ts");
/* provided dependency */ var __react_refresh_utils__ = __webpack_require__(/*! ../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js */ "../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js");
__webpack_require__.$Refresh$.runtime = __webpack_require__(/*! ../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js */ "../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js");






const buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_3__.cva)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive", {
    variants: {
        variant: {
            default: "bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",
            destructive: "bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",
            outline: "border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",
            secondary: "bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",
            ghost: "hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",
            link: "text-primary underline-offset-4 hover:underline"
        },
        size: {
            default: "h-9 px-4 py-2 has-[>svg]:px-3",
            sm: "h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",
            lg: "h-10 rounded-md px-6 has-[>svg]:px-4",
            icon: "size-9"
        }
    },
    defaultVariants: {
        variant: "default",
        size: "default"
    }
});
function Button({ className, variant, size, asChild = false, ...props }) {
    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : "button";
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {
        "data-slot": "button",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(buttonVariants({
            variant,
            size,
            className
        })),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\packages\\design-system\\components\\ui\\button.tsx",
        lineNumber: 51,
        columnNumber: 5
    }, this);
}
_c = Button;

Button.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "Button",
    "props": {
        "asChild": {
            "required": false,
            "tsType": {
                "name": "boolean"
            },
            "description": "",
            "defaultValue": {
                "value": "false",
                "computed": false
            }
        }
    }
};
var _c;
__webpack_require__.$Refresh$.register(_c, "Button");


const $ReactRefreshModuleId$ = __webpack_require__.$Refresh$.moduleId;
const $ReactRefreshCurrentExports$ = __react_refresh_utils__.getModuleExports(
	$ReactRefreshModuleId$
);

function $ReactRefreshModuleRuntime$(exports) {
	if (true) {
		let errorOverlay;
		if (true) {
			errorOverlay = false;
		}
		let testMode;
		if (typeof __react_refresh_test__ !== 'undefined') {
			testMode = __react_refresh_test__;
		}
		return __react_refresh_utils__.executeRuntime(
			exports,
			$ReactRefreshModuleId$,
			module.hot,
			errorOverlay,
			testMode
		);
	}
}

if (typeof Promise !== 'undefined' && $ReactRefreshCurrentExports$ instanceof Promise) {
	$ReactRefreshCurrentExports$.then($ReactRefreshModuleRuntime$);
} else {
	$ReactRefreshModuleRuntime$($ReactRefreshCurrentExports$);
}

/***/ }),

/***/ "../../packages/design-system/components/ui/pagination.tsx":
/*!*****************************************************************!*\
  !*** ../../packages/design-system/components/ui/pagination.tsx ***!
  \*****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Pagination: () => (/* binding */ Pagination),
/* harmony export */   PaginationContent: () => (/* binding */ PaginationContent),
/* harmony export */   PaginationEllipsis: () => (/* binding */ PaginationEllipsis),
/* harmony export */   PaginationItem: () => (/* binding */ PaginationItem),
/* harmony export */   PaginationLink: () => (/* binding */ PaginationLink),
/* harmony export */   PaginationNext: () => (/* binding */ PaginationNext),
/* harmony export */   PaginationPrevious: () => (/* binding */ PaginationPrevious)
/* harmony export */ });
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/jsx-dev-runtime.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _barrel_optimize_names_ChevronLeftIcon_ChevronRightIcon_MoreHorizontalIcon_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeftIcon,ChevronRightIcon,MoreHorizontalIcon!=!lucide-react */ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/chevron-left.js");
/* harmony import */ var _barrel_optimize_names_ChevronLeftIcon_ChevronRightIcon_MoreHorizontalIcon_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeftIcon,ChevronRightIcon,MoreHorizontalIcon!=!lucide-react */ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/chevron-right.js");
/* harmony import */ var _barrel_optimize_names_ChevronLeftIcon_ChevronRightIcon_MoreHorizontalIcon_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeftIcon,ChevronRightIcon,MoreHorizontalIcon!=!lucide-react */ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/ellipsis.js");
/* harmony import */ var _repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @repo/design-system/lib/utils */ "../../packages/design-system/lib/utils.ts");
/* harmony import */ var _repo_design_system_components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @repo/design-system/components/ui/button */ "../../packages/design-system/components/ui/button.tsx");
/* provided dependency */ var __react_refresh_utils__ = __webpack_require__(/*! ../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js */ "../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js");
__webpack_require__.$Refresh$.runtime = __webpack_require__(/*! ../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js */ "../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js");






function Pagination({ className, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("nav", {
        role: "navigation",
        "aria-label": "pagination",
        "data-slot": "pagination",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)("mx-auto flex w-full justify-center", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\packages\\design-system\\components\\ui\\pagination.tsx",
        lineNumber: 13,
        columnNumber: 5
    }, this);
}
_c = Pagination;
function PaginationContent({ className, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("ul", {
        "data-slot": "pagination-content",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)("flex flex-row items-center gap-1", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\packages\\design-system\\components\\ui\\pagination.tsx",
        lineNumber: 28,
        columnNumber: 5
    }, this);
}
_c1 = PaginationContent;
function PaginationItem({ ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("li", {
        "data-slot": "pagination-item",
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\packages\\design-system\\components\\ui\\pagination.tsx",
        lineNumber: 37,
        columnNumber: 10
    }, this);
}
_c2 = PaginationItem;
function PaginationLink({ className, isActive, size = "icon", ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("a", {
        "aria-current": isActive ? "page" : undefined,
        "data-slot": "pagination-link",
        "data-active": isActive,
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)((0,_repo_design_system_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.buttonVariants)({
            variant: isActive ? "outline" : "ghost",
            size
        }), className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\packages\\design-system\\components\\ui\\pagination.tsx",
        lineNumber: 52,
        columnNumber: 5
    }, this);
}
_c3 = PaginationLink;
function PaginationPrevious({ className, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PaginationLink, {
        "aria-label": "Go to previous page",
        size: "default",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)("gap-1 px-2.5 sm:pl-2.5", className),
        ...props,
        children: [
            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeftIcon_ChevronRightIcon_MoreHorizontalIcon_lucide_react__WEBPACK_IMPORTED_MODULE_4__["default"], {}, void 0, false, {
                fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\packages\\design-system\\components\\ui\\pagination.tsx",
                lineNumber: 79,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("span", {
                className: "hidden sm:block",
                children: "Previous"
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\packages\\design-system\\components\\ui\\pagination.tsx",
                lineNumber: 80,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\packages\\design-system\\components\\ui\\pagination.tsx",
        lineNumber: 73,
        columnNumber: 5
    }, this);
}
_c4 = PaginationPrevious;
function PaginationNext({ className, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PaginationLink, {
        "aria-label": "Go to next page",
        size: "default",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)("gap-1 px-2.5 sm:pr-2.5", className),
        ...props,
        children: [
            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("span", {
                className: "hidden sm:block",
                children: "Next"
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\packages\\design-system\\components\\ui\\pagination.tsx",
                lineNumber: 96,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeftIcon_ChevronRightIcon_MoreHorizontalIcon_lucide_react__WEBPACK_IMPORTED_MODULE_5__["default"], {}, void 0, false, {
                fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\packages\\design-system\\components\\ui\\pagination.tsx",
                lineNumber: 97,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\packages\\design-system\\components\\ui\\pagination.tsx",
        lineNumber: 90,
        columnNumber: 5
    }, this);
}
_c5 = PaginationNext;
function PaginationEllipsis({ className, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("span", {
        "aria-hidden": true,
        "data-slot": "pagination-ellipsis",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)("flex size-9 items-center justify-center", className),
        ...props,
        children: [
            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeftIcon_ChevronRightIcon_MoreHorizontalIcon_lucide_react__WEBPACK_IMPORTED_MODULE_6__["default"], {
                className: "size-4"
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\packages\\design-system\\components\\ui\\pagination.tsx",
                lineNumber: 113,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("span", {
                className: "sr-only",
                children: "More pages"
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\packages\\design-system\\components\\ui\\pagination.tsx",
                lineNumber: 114,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\packages\\design-system\\components\\ui\\pagination.tsx",
        lineNumber: 107,
        columnNumber: 5
    }, this);
}
_c6 = PaginationEllipsis;

Pagination.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "Pagination"
};
PaginationContent.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "PaginationContent"
};
PaginationLink.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "PaginationLink",
    "props": {
        "isActive": {
            "required": false,
            "tsType": {
                "name": "boolean"
            },
            "description": ""
        },
        "size": {
            "defaultValue": {
                "value": "\"icon\"",
                "computed": false
            },
            "required": false
        }
    }
};
PaginationItem.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "PaginationItem"
};
PaginationPrevious.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "PaginationPrevious"
};
PaginationNext.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "PaginationNext"
};
PaginationEllipsis.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "PaginationEllipsis"
};
var _c, _c1, _c2, _c3, _c4, _c5, _c6;
__webpack_require__.$Refresh$.register(_c, "Pagination");
__webpack_require__.$Refresh$.register(_c1, "PaginationContent");
__webpack_require__.$Refresh$.register(_c2, "PaginationItem");
__webpack_require__.$Refresh$.register(_c3, "PaginationLink");
__webpack_require__.$Refresh$.register(_c4, "PaginationPrevious");
__webpack_require__.$Refresh$.register(_c5, "PaginationNext");
__webpack_require__.$Refresh$.register(_c6, "PaginationEllipsis");


const $ReactRefreshModuleId$ = __webpack_require__.$Refresh$.moduleId;
const $ReactRefreshCurrentExports$ = __react_refresh_utils__.getModuleExports(
	$ReactRefreshModuleId$
);

function $ReactRefreshModuleRuntime$(exports) {
	if (true) {
		let errorOverlay;
		if (true) {
			errorOverlay = false;
		}
		let testMode;
		if (typeof __react_refresh_test__ !== 'undefined') {
			testMode = __react_refresh_test__;
		}
		return __react_refresh_utils__.executeRuntime(
			exports,
			$ReactRefreshModuleId$,
			module.hot,
			errorOverlay,
			testMode
		);
	}
}

if (typeof Promise !== 'undefined' && $ReactRefreshCurrentExports$ instanceof Promise) {
	$ReactRefreshCurrentExports$.then($ReactRefreshModuleRuntime$);
} else {
	$ReactRefreshModuleRuntime$($ReactRefreshCurrentExports$);
}

/***/ }),

/***/ "./stories/pagination.stories.tsx":
/*!****************************************!*\
  !*** ./stories/pagination.stories.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Default: () => (/* binding */ Default),
/* harmony export */   __namedExportsOrder: () => (/* binding */ __namedExportsOrder),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/jsx-dev-runtime.js");
/* harmony import */ var _repo_design_system_components_ui_pagination__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @repo/design-system/components/ui/pagination */ "../../packages/design-system/components/ui/pagination.tsx");
/* provided dependency */ var __react_refresh_utils__ = __webpack_require__(/*! ../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js */ "../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js");
__webpack_require__.$Refresh$.runtime = __webpack_require__(/*! ../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js */ "../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js");



/**
 * Pagination with page navigation, next and previous links.
 */
const meta = {
  title: 'ui/Pagination',
  component: _repo_design_system_components_ui_pagination__WEBPACK_IMPORTED_MODULE_1__.Pagination,
  tags: ['autodocs'],
  argTypes: {},
  render: args => /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_pagination__WEBPACK_IMPORTED_MODULE_1__.Pagination, {
    ...args,
    children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_pagination__WEBPACK_IMPORTED_MODULE_1__.PaginationContent, {
      children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_pagination__WEBPACK_IMPORTED_MODULE_1__.PaginationItem, {
        children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_pagination__WEBPACK_IMPORTED_MODULE_1__.PaginationPrevious, {
          href: "#"
        }, void 0, false, {
          fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\pagination.stories.tsx",
          lineNumber: 25,
          columnNumber: 11
        }, undefined)
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\pagination.stories.tsx",
        lineNumber: 24,
        columnNumber: 9
      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_pagination__WEBPACK_IMPORTED_MODULE_1__.PaginationItem, {
        children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_pagination__WEBPACK_IMPORTED_MODULE_1__.PaginationLink, {
          href: "#",
          children: "1"
        }, void 0, false, {
          fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\pagination.stories.tsx",
          lineNumber: 28,
          columnNumber: 11
        }, undefined)
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\pagination.stories.tsx",
        lineNumber: 27,
        columnNumber: 9
      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_pagination__WEBPACK_IMPORTED_MODULE_1__.PaginationItem, {
        children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_pagination__WEBPACK_IMPORTED_MODULE_1__.PaginationLink, {
          href: "#",
          children: "2"
        }, void 0, false, {
          fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\pagination.stories.tsx",
          lineNumber: 31,
          columnNumber: 11
        }, undefined)
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\pagination.stories.tsx",
        lineNumber: 30,
        columnNumber: 9
      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_pagination__WEBPACK_IMPORTED_MODULE_1__.PaginationItem, {
        children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_pagination__WEBPACK_IMPORTED_MODULE_1__.PaginationLink, {
          href: "#",
          children: "3"
        }, void 0, false, {
          fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\pagination.stories.tsx",
          lineNumber: 34,
          columnNumber: 11
        }, undefined)
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\pagination.stories.tsx",
        lineNumber: 33,
        columnNumber: 9
      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_pagination__WEBPACK_IMPORTED_MODULE_1__.PaginationItem, {
        children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_pagination__WEBPACK_IMPORTED_MODULE_1__.PaginationEllipsis, {}, void 0, false, {
          fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\pagination.stories.tsx",
          lineNumber: 37,
          columnNumber: 11
        }, undefined)
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\pagination.stories.tsx",
        lineNumber: 36,
        columnNumber: 9
      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_pagination__WEBPACK_IMPORTED_MODULE_1__.PaginationItem, {
        children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_pagination__WEBPACK_IMPORTED_MODULE_1__.PaginationNext, {
          href: "#"
        }, void 0, false, {
          fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\pagination.stories.tsx",
          lineNumber: 40,
          columnNumber: 11
        }, undefined)
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\pagination.stories.tsx",
        lineNumber: 39,
        columnNumber: 9
      }, undefined)]
    }, void 0, true, {
      fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\pagination.stories.tsx",
      lineNumber: 23,
      columnNumber: 7
    }, undefined)
  }, void 0, false, {
    fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\pagination.stories.tsx",
    lineNumber: 22,
    columnNumber: 5
  }, undefined),
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: "Pagination with page navigation, next and previous links."
      }
    }
  }
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (meta);
/**
 * The default form of the pagination.
 */
const Default = {};
;
const __namedExportsOrder = ["Default"];
Default.parameters = {
  ...Default.parameters,
  docs: {
    ...Default.parameters?.docs,
    source: {
      originalSource: "{}",
      ...Default.parameters?.docs?.source
    },
    description: {
      story: "The default form of the pagination.",
      ...Default.parameters?.docs?.description
    }
  }
};

const $ReactRefreshModuleId$ = __webpack_require__.$Refresh$.moduleId;
const $ReactRefreshCurrentExports$ = __react_refresh_utils__.getModuleExports(
	$ReactRefreshModuleId$
);

function $ReactRefreshModuleRuntime$(exports) {
	if (true) {
		let errorOverlay;
		if (true) {
			errorOverlay = false;
		}
		let testMode;
		if (typeof __react_refresh_test__ !== 'undefined') {
			testMode = __react_refresh_test__;
		}
		return __react_refresh_utils__.executeRuntime(
			exports,
			$ReactRefreshModuleId$,
			module.hot,
			errorOverlay,
			testMode
		);
	}
}

if (typeof Promise !== 'undefined' && $ReactRefreshCurrentExports$ instanceof Promise) {
	$ReactRefreshCurrentExports$.then($ReactRefreshModuleRuntime$);
} else {
	$ReactRefreshModuleRuntime$($ReactRefreshCurrentExports$);
}

/***/ })

}]);
//# sourceMappingURL=pagination-stories.iframe.bundle.js.map