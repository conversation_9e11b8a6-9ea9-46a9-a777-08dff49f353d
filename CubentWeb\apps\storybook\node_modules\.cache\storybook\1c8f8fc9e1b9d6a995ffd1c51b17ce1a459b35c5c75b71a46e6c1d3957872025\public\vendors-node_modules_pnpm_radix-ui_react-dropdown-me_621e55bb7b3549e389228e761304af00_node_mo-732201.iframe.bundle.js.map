{"version": 3, "file": "vendors-node_modules_pnpm_radix-ui_react-dropdown-me_621e55bb7b3549e389228e761304af00_node_mo-732201.iframe.bundle.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAmCA;AACA;;;;;;;;;;;;;;;;;AChTA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "sources": ["webpack://storybook/../../node_modules/.pnpm/@radix-ui+react-dropdown-me_621e55bb7b3549e389228e761304af00/node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs", "webpack://storybook/../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/plus.js"], "sourcesContent": ["\"use client\";\n\n// src/dropdown-menu.tsx\nimport * as React from \"react\";\nimport { composeEventHandlers } from \"@radix-ui/primitive\";\nimport { composeRefs } from \"@radix-ui/react-compose-refs\";\nimport { createContextScope } from \"@radix-ui/react-context\";\nimport { useControllableState } from \"@radix-ui/react-use-controllable-state\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport * as MenuPrimitive from \"@radix-ui/react-menu\";\nimport { createMenuScope } from \"@radix-ui/react-menu\";\nimport { useId } from \"@radix-ui/react-id\";\nimport { jsx } from \"react/jsx-runtime\";\nvar DROPDOWN_MENU_NAME = \"DropdownMenu\";\nvar [createDropdownMenuContext, createDropdownMenuScope] = createContextScope(\n  DROPDOWN_MENU_NAME,\n  [createMenuScope]\n);\nvar useMenuScope = createMenuScope();\nvar [DropdownMenuProvider, useDropdownMenuContext] = createDropdownMenuContext(DROPDOWN_MENU_NAME);\nvar DropdownMenu = (props) => {\n  const {\n    __scopeDropdownMenu,\n    children,\n    dir,\n    open: openProp,\n    defaultOpen,\n    onOpenChange,\n    modal = true\n  } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  const triggerRef = React.useRef(null);\n  const [open, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen ?? false,\n    onChange: onOpenChange,\n    caller: DROPDOWN_MENU_NAME\n  });\n  return /* @__PURE__ */ jsx(\n    DropdownMenuProvider,\n    {\n      scope: __scopeDropdownMenu,\n      triggerId: useId(),\n      triggerRef,\n      contentId: useId(),\n      open,\n      onOpenChange: setOpen,\n      onOpenToggle: React.useCallback(() => setOpen((prevOpen) => !prevOpen), [setOpen]),\n      modal,\n      children: /* @__PURE__ */ jsx(MenuPrimitive.Root, { ...menuScope, open, onOpenChange: setOpen, dir, modal, children })\n    }\n  );\n};\nDropdownMenu.displayName = DROPDOWN_MENU_NAME;\nvar TRIGGER_NAME = \"DropdownMenuTrigger\";\nvar DropdownMenuTrigger = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeDropdownMenu, disabled = false, ...triggerProps } = props;\n    const context = useDropdownMenuContext(TRIGGER_NAME, __scopeDropdownMenu);\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return /* @__PURE__ */ jsx(MenuPrimitive.Anchor, { asChild: true, ...menuScope, children: /* @__PURE__ */ jsx(\n      Primitive.button,\n      {\n        type: \"button\",\n        id: context.triggerId,\n        \"aria-haspopup\": \"menu\",\n        \"aria-expanded\": context.open,\n        \"aria-controls\": context.open ? context.contentId : void 0,\n        \"data-state\": context.open ? \"open\" : \"closed\",\n        \"data-disabled\": disabled ? \"\" : void 0,\n        disabled,\n        ...triggerProps,\n        ref: composeRefs(forwardedRef, context.triggerRef),\n        onPointerDown: composeEventHandlers(props.onPointerDown, (event) => {\n          if (!disabled && event.button === 0 && event.ctrlKey === false) {\n            context.onOpenToggle();\n            if (!context.open) event.preventDefault();\n          }\n        }),\n        onKeyDown: composeEventHandlers(props.onKeyDown, (event) => {\n          if (disabled) return;\n          if ([\"Enter\", \" \"].includes(event.key)) context.onOpenToggle();\n          if (event.key === \"ArrowDown\") context.onOpenChange(true);\n          if ([\"Enter\", \" \", \"ArrowDown\"].includes(event.key)) event.preventDefault();\n        })\n      }\n    ) });\n  }\n);\nDropdownMenuTrigger.displayName = TRIGGER_NAME;\nvar PORTAL_NAME = \"DropdownMenuPortal\";\nvar DropdownMenuPortal = (props) => {\n  const { __scopeDropdownMenu, ...portalProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return /* @__PURE__ */ jsx(MenuPrimitive.Portal, { ...menuScope, ...portalProps });\n};\nDropdownMenuPortal.displayName = PORTAL_NAME;\nvar CONTENT_NAME = \"DropdownMenuContent\";\nvar DropdownMenuContent = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeDropdownMenu, ...contentProps } = props;\n    const context = useDropdownMenuContext(CONTENT_NAME, __scopeDropdownMenu);\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    const hasInteractedOutsideRef = React.useRef(false);\n    return /* @__PURE__ */ jsx(\n      MenuPrimitive.Content,\n      {\n        id: context.contentId,\n        \"aria-labelledby\": context.triggerId,\n        ...menuScope,\n        ...contentProps,\n        ref: forwardedRef,\n        onCloseAutoFocus: composeEventHandlers(props.onCloseAutoFocus, (event) => {\n          if (!hasInteractedOutsideRef.current) context.triggerRef.current?.focus();\n          hasInteractedOutsideRef.current = false;\n          event.preventDefault();\n        }),\n        onInteractOutside: composeEventHandlers(props.onInteractOutside, (event) => {\n          const originalEvent = event.detail.originalEvent;\n          const ctrlLeftClick = originalEvent.button === 0 && originalEvent.ctrlKey === true;\n          const isRightClick = originalEvent.button === 2 || ctrlLeftClick;\n          if (!context.modal || isRightClick) hasInteractedOutsideRef.current = true;\n        }),\n        style: {\n          ...props.style,\n          // re-namespace exposed content custom properties\n          ...{\n            \"--radix-dropdown-menu-content-transform-origin\": \"var(--radix-popper-transform-origin)\",\n            \"--radix-dropdown-menu-content-available-width\": \"var(--radix-popper-available-width)\",\n            \"--radix-dropdown-menu-content-available-height\": \"var(--radix-popper-available-height)\",\n            \"--radix-dropdown-menu-trigger-width\": \"var(--radix-popper-anchor-width)\",\n            \"--radix-dropdown-menu-trigger-height\": \"var(--radix-popper-anchor-height)\"\n          }\n        }\n      }\n    );\n  }\n);\nDropdownMenuContent.displayName = CONTENT_NAME;\nvar GROUP_NAME = \"DropdownMenuGroup\";\nvar DropdownMenuGroup = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeDropdownMenu, ...groupProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return /* @__PURE__ */ jsx(MenuPrimitive.Group, { ...menuScope, ...groupProps, ref: forwardedRef });\n  }\n);\nDropdownMenuGroup.displayName = GROUP_NAME;\nvar LABEL_NAME = \"DropdownMenuLabel\";\nvar DropdownMenuLabel = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeDropdownMenu, ...labelProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return /* @__PURE__ */ jsx(MenuPrimitive.Label, { ...menuScope, ...labelProps, ref: forwardedRef });\n  }\n);\nDropdownMenuLabel.displayName = LABEL_NAME;\nvar ITEM_NAME = \"DropdownMenuItem\";\nvar DropdownMenuItem = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeDropdownMenu, ...itemProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return /* @__PURE__ */ jsx(MenuPrimitive.Item, { ...menuScope, ...itemProps, ref: forwardedRef });\n  }\n);\nDropdownMenuItem.displayName = ITEM_NAME;\nvar CHECKBOX_ITEM_NAME = \"DropdownMenuCheckboxItem\";\nvar DropdownMenuCheckboxItem = React.forwardRef((props, forwardedRef) => {\n  const { __scopeDropdownMenu, ...checkboxItemProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return /* @__PURE__ */ jsx(MenuPrimitive.CheckboxItem, { ...menuScope, ...checkboxItemProps, ref: forwardedRef });\n});\nDropdownMenuCheckboxItem.displayName = CHECKBOX_ITEM_NAME;\nvar RADIO_GROUP_NAME = \"DropdownMenuRadioGroup\";\nvar DropdownMenuRadioGroup = React.forwardRef((props, forwardedRef) => {\n  const { __scopeDropdownMenu, ...radioGroupProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return /* @__PURE__ */ jsx(MenuPrimitive.RadioGroup, { ...menuScope, ...radioGroupProps, ref: forwardedRef });\n});\nDropdownMenuRadioGroup.displayName = RADIO_GROUP_NAME;\nvar RADIO_ITEM_NAME = \"DropdownMenuRadioItem\";\nvar DropdownMenuRadioItem = React.forwardRef((props, forwardedRef) => {\n  const { __scopeDropdownMenu, ...radioItemProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return /* @__PURE__ */ jsx(MenuPrimitive.RadioItem, { ...menuScope, ...radioItemProps, ref: forwardedRef });\n});\nDropdownMenuRadioItem.displayName = RADIO_ITEM_NAME;\nvar INDICATOR_NAME = \"DropdownMenuItemIndicator\";\nvar DropdownMenuItemIndicator = React.forwardRef((props, forwardedRef) => {\n  const { __scopeDropdownMenu, ...itemIndicatorProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return /* @__PURE__ */ jsx(MenuPrimitive.ItemIndicator, { ...menuScope, ...itemIndicatorProps, ref: forwardedRef });\n});\nDropdownMenuItemIndicator.displayName = INDICATOR_NAME;\nvar SEPARATOR_NAME = \"DropdownMenuSeparator\";\nvar DropdownMenuSeparator = React.forwardRef((props, forwardedRef) => {\n  const { __scopeDropdownMenu, ...separatorProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return /* @__PURE__ */ jsx(MenuPrimitive.Separator, { ...menuScope, ...separatorProps, ref: forwardedRef });\n});\nDropdownMenuSeparator.displayName = SEPARATOR_NAME;\nvar ARROW_NAME = \"DropdownMenuArrow\";\nvar DropdownMenuArrow = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeDropdownMenu, ...arrowProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return /* @__PURE__ */ jsx(MenuPrimitive.Arrow, { ...menuScope, ...arrowProps, ref: forwardedRef });\n  }\n);\nDropdownMenuArrow.displayName = ARROW_NAME;\nvar DropdownMenuSub = (props) => {\n  const { __scopeDropdownMenu, children, open: openProp, onOpenChange, defaultOpen } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  const [open, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen ?? false,\n    onChange: onOpenChange,\n    caller: \"DropdownMenuSub\"\n  });\n  return /* @__PURE__ */ jsx(MenuPrimitive.Sub, { ...menuScope, open, onOpenChange: setOpen, children });\n};\nvar SUB_TRIGGER_NAME = \"DropdownMenuSubTrigger\";\nvar DropdownMenuSubTrigger = React.forwardRef((props, forwardedRef) => {\n  const { __scopeDropdownMenu, ...subTriggerProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return /* @__PURE__ */ jsx(MenuPrimitive.SubTrigger, { ...menuScope, ...subTriggerProps, ref: forwardedRef });\n});\nDropdownMenuSubTrigger.displayName = SUB_TRIGGER_NAME;\nvar SUB_CONTENT_NAME = \"DropdownMenuSubContent\";\nvar DropdownMenuSubContent = React.forwardRef((props, forwardedRef) => {\n  const { __scopeDropdownMenu, ...subContentProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return /* @__PURE__ */ jsx(\n    MenuPrimitive.SubContent,\n    {\n      ...menuScope,\n      ...subContentProps,\n      ref: forwardedRef,\n      style: {\n        ...props.style,\n        // re-namespace exposed content custom properties\n        ...{\n          \"--radix-dropdown-menu-content-transform-origin\": \"var(--radix-popper-transform-origin)\",\n          \"--radix-dropdown-menu-content-available-width\": \"var(--radix-popper-available-width)\",\n          \"--radix-dropdown-menu-content-available-height\": \"var(--radix-popper-available-height)\",\n          \"--radix-dropdown-menu-trigger-width\": \"var(--radix-popper-anchor-width)\",\n          \"--radix-dropdown-menu-trigger-height\": \"var(--radix-popper-anchor-height)\"\n        }\n      }\n    }\n  );\n});\nDropdownMenuSubContent.displayName = SUB_CONTENT_NAME;\nvar Root2 = DropdownMenu;\nvar Trigger = DropdownMenuTrigger;\nvar Portal2 = DropdownMenuPortal;\nvar Content2 = DropdownMenuContent;\nvar Group2 = DropdownMenuGroup;\nvar Label2 = DropdownMenuLabel;\nvar Item2 = DropdownMenuItem;\nvar CheckboxItem2 = DropdownMenuCheckboxItem;\nvar RadioGroup2 = DropdownMenuRadioGroup;\nvar RadioItem2 = DropdownMenuRadioItem;\nvar ItemIndicator2 = DropdownMenuItemIndicator;\nvar Separator2 = DropdownMenuSeparator;\nvar Arrow2 = DropdownMenuArrow;\nvar Sub2 = DropdownMenuSub;\nvar SubTrigger2 = DropdownMenuSubTrigger;\nvar SubContent2 = DropdownMenuSubContent;\nexport {\n  Arrow2 as Arrow,\n  CheckboxItem2 as CheckboxItem,\n  Content2 as Content,\n  DropdownMenu,\n  DropdownMenuArrow,\n  DropdownMenuCheckboxItem,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuItem,\n  DropdownMenuItemIndicator,\n  DropdownMenuLabel,\n  DropdownMenuPortal,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuSub,\n  DropdownMenuSubContent,\n  DropdownMenuSubTrigger,\n  DropdownMenuTrigger,\n  Group2 as Group,\n  Item2 as Item,\n  ItemIndicator2 as ItemIndicator,\n  Label2 as Label,\n  Portal2 as Portal,\n  RadioGroup2 as RadioGroup,\n  RadioItem2 as RadioItem,\n  Root2 as Root,\n  Separator2 as Separator,\n  Sub2 as Sub,\n  SubContent2 as SubContent,\n  SubTrigger2 as SubTrigger,\n  Trigger,\n  createDropdownMenuScope\n};\n//# sourceMappingURL=index.mjs.map\n", "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"path\", { d: \"M5 12h14\", key: \"1ays0h\" }],\n  [\"path\", { d: \"M12 5v14\", key: \"s699le\" }]\n];\nconst Plus = createLucideIcon(\"plus\", __iconNode);\n\nexport { __iconNode, Plus as default };\n//# sourceMappingURL=plus.js.map\n"], "names": [], "sourceRoot": ""}