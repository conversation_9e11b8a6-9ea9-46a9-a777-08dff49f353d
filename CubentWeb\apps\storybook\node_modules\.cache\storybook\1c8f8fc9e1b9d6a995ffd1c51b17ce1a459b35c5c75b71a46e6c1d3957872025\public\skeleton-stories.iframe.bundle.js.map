{"version": 3, "file": "skeleton-stories.iframe.bundle.js", "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;AAEA;AACA;AAEA;AACA;AACA;;;;;;AAGA;AARA;AAUA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACVA;AAEA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AAIA;;;AAGA;AACA;AACA;AACA;AAAA;AAAA;;;;;AACA;AAAA;AACA;AAAA;AAAA;;;;;AACA;AAAA;AAAA;;;;;;;;;;;;;;;;AAIA", "sources": ["webpack://storybook/../../packages/design-system/components/ui/skeleton.tsx", "webpack://storybook/./stories/skeleton.stories.tsx"], "sourcesContent": ["import { cn } from \"@repo/design-system/lib/utils\"\n\nfunction Skeleton({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"skeleton\"\n      className={cn(\"bg-accent animate-pulse rounded-md\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Skeleton }\n", "import type { <PERSON>a, StoryObj } from '@storybook/react';\n\nimport { Skeleton } from '@repo/design-system/components/ui/skeleton';\n\n/**\n * Use to show a placeholder while content is loading.\n */\nconst meta = {\n  title: 'ui/Skeleton',\n  component: Skeleton,\n  tags: ['autodocs'],\n  argTypes: {},\n  parameters: {\n    layout: 'centered',\n  },\n} satisfies Meta<typeof Skeleton>;\n\nexport default meta;\n\ntype Story = StoryObj<typeof Skeleton>;\n\n/**\n * The default form of the skeleton.\n */\nexport const Default: Story = {\n  render: (args) => (\n    <div className=\"flex items-center space-x-4\">\n      <Skeleton {...args} className=\"h-12 w-12 rounded-full\" />\n      <div className=\"space-y-2\">\n        <Skeleton {...args} className=\"h-4 w-[250px]\" />\n        <Skeleton {...args} className=\"h-4 w-[200px]\" />\n      </div>\n    </div>\n  ),\n};\n"], "names": [], "sourceRoot": ""}