{"version": 3, "file": "popover-stories.iframe.bundle.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AAEA;AACA;AAEA;AAEA;AAGA;AAAA;AAAA;;;;;;AACA;AAJA;AAMA;AAGA;AAAA;AAAA;;;;;;AACA;;AAEA;AAMA;AAEA;AACA;AACA;AACA;AACA;AAIA;;;;;;;;;;;AAIA;AApBA;AAsBA;AAGA;AAAA;AAAA;;;;;;AACA;AAJA;AAMA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7CA;AAMA;;;AAGA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAAA;;;;;AACA;AAAA;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AAIA;;;AAGA", "sources": ["webpack://storybook/../../packages/design-system/components/ui/popover.tsx", "webpack://storybook/./stories/popover.stories.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as PopoverPrimitive from \"@radix-ui/react-popover\"\n\nimport { cn } from \"@repo/design-system/lib/utils\"\n\nfunction Popover({\n  ...props\n}: React.ComponentProps<typeof PopoverPrimitive.Root>) {\n  return <PopoverPrimitive.Root data-slot=\"popover\" {...props} />\n}\n\nfunction PopoverTrigger({\n  ...props\n}: React.ComponentProps<typeof PopoverPrimitive.Trigger>) {\n  return <PopoverPrimitive.Trigger data-slot=\"popover-trigger\" {...props} />\n}\n\nfunction PopoverContent({\n  className,\n  align = \"center\",\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof PopoverPrimitive.Content>) {\n  return (\n    <PopoverPrimitive.Portal>\n      <PopoverPrimitive.Content\n        data-slot=\"popover-content\"\n        align={align}\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden\",\n          className\n        )}\n        {...props}\n      />\n    </PopoverPrimitive.Portal>\n  )\n}\n\nfunction PopoverAnchor({\n  ...props\n}: React.ComponentProps<typeof PopoverPrimitive.Anchor>) {\n  return <PopoverPrimitive.Anchor data-slot=\"popover-anchor\" {...props} />\n}\n\nexport { Popover, PopoverTrigger, PopoverContent, PopoverAnchor }\n", "import type { <PERSON>a, StoryObj } from '@storybook/react';\n\nimport {\n  Popover,\n  PopoverContent,\n  PopoverTrigger,\n} from '@repo/design-system/components/ui/popover';\n\n/**\n * Displays rich content in a portal, triggered by a button.\n */\nconst meta = {\n  title: 'ui/Popover',\n  component: Popover,\n  tags: ['autodocs'],\n  argTypes: {},\n\n  render: (args) => (\n    <Popover {...args}>\n      <PopoverTrigger>Open</PopoverTrigger>\n      <PopoverContent>Place content for the popover here.</PopoverContent>\n    </Popover>\n  ),\n  parameters: {\n    layout: 'centered',\n  },\n} satisfies Meta<typeof Popover>;\n\nexport default meta;\n\ntype Story = StoryObj<typeof meta>;\n\n/**\n * The default form of the popover.\n */\nexport const Default: Story = {};\n"], "names": [], "sourceRoot": ""}