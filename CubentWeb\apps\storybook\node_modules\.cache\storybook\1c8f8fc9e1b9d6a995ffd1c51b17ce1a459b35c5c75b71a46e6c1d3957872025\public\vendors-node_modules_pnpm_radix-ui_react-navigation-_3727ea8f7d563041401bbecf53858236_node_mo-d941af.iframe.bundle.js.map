{"version": 3, "file": "vendors-node_modules_pnpm_radix-ui_react-navigation-_3727ea8f7d563041401bbecf53858236_node_mo-d941af.iframe.bundle.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAqBA;AACA;;;;;;;;;;;;;;;;;ACryBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;;;;;;;;;;;;;;;;;ACfA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;ACvDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;AC1CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;AC7BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;ACpBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;ACbA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "sources": ["webpack://storybook/../../node_modules/.pnpm/@radix-ui+react-navigation-_3727ea8f7d563041401bbecf53858236/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs", "webpack://storybook/../../node_modules/.pnpm/@radix-ui+react-use-previou_791288859aab8756064fe392350c2e0c/node_modules/@radix-ui/react-use-previous/dist/index.mjs", "webpack://storybook/../../node_modules/.pnpm/class-variance-authority@0.7.1/node_modules/class-variance-authority/dist/index.mjs", "webpack://storybook/../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/Icon.js", "webpack://storybook/../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/createLucideIcon.js", "webpack://storybook/../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/defaultAttributes.js", "webpack://storybook/../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/chevron-down.js", "webpack://storybook/../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/shared/src/utils.js"], "sourcesContent": ["\"use client\";\n\n// src/navigation-menu.tsx\nimport * as React from \"react\";\nimport ReactDOM from \"react-dom\";\nimport { createContextScope } from \"@radix-ui/react-context\";\nimport { composeEventHandlers } from \"@radix-ui/primitive\";\nimport { Primitive, dispatchDiscreteCustomEvent } from \"@radix-ui/react-primitive\";\nimport { useControllableState } from \"@radix-ui/react-use-controllable-state\";\nimport { composeRefs, useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { useDirection } from \"@radix-ui/react-direction\";\nimport { Presence } from \"@radix-ui/react-presence\";\nimport { useId } from \"@radix-ui/react-id\";\nimport { createCollection } from \"@radix-ui/react-collection\";\nimport { DismissableLayer } from \"@radix-ui/react-dismissable-layer\";\nimport { usePrevious } from \"@radix-ui/react-use-previous\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nimport { useCallbackRef } from \"@radix-ui/react-use-callback-ref\";\nimport * as VisuallyHiddenPrimitive from \"@radix-ui/react-visually-hidden\";\nimport { Fragment, jsx, jsxs } from \"react/jsx-runtime\";\nvar NAVIGATION_MENU_NAME = \"NavigationMenu\";\nvar [Collection, useCollection, createCollectionScope] = createCollection(NAVIGATION_MENU_NAME);\nvar [FocusGroupCollection, useFocusGroupCollection, createFocusGroupCollectionScope] = createCollection(NAVIGATION_MENU_NAME);\nvar [createNavigationMenuContext, createNavigationMenuScope] = createContextScope(\n  NAVIGATION_MENU_NAME,\n  [createCollectionScope, createFocusGroupCollectionScope]\n);\nvar [NavigationMenuProviderImpl, useNavigationMenuContext] = createNavigationMenuContext(NAVIGATION_MENU_NAME);\nvar [ViewportContentProvider, useViewportContentContext] = createNavigationMenuContext(NAVIGATION_MENU_NAME);\nvar NavigationMenu = React.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      __scopeNavigationMenu,\n      value: valueProp,\n      onValueChange,\n      defaultValue,\n      delayDuration = 200,\n      skipDelayDuration = 300,\n      orientation = \"horizontal\",\n      dir,\n      ...NavigationMenuProps\n    } = props;\n    const [navigationMenu, setNavigationMenu] = React.useState(null);\n    const composedRef = useComposedRefs(forwardedRef, (node) => setNavigationMenu(node));\n    const direction = useDirection(dir);\n    const openTimerRef = React.useRef(0);\n    const closeTimerRef = React.useRef(0);\n    const skipDelayTimerRef = React.useRef(0);\n    const [isOpenDelayed, setIsOpenDelayed] = React.useState(true);\n    const [value, setValue] = useControllableState({\n      prop: valueProp,\n      onChange: (value2) => {\n        const isOpen = value2 !== \"\";\n        const hasSkipDelayDuration = skipDelayDuration > 0;\n        if (isOpen) {\n          window.clearTimeout(skipDelayTimerRef.current);\n          if (hasSkipDelayDuration) setIsOpenDelayed(false);\n        } else {\n          window.clearTimeout(skipDelayTimerRef.current);\n          skipDelayTimerRef.current = window.setTimeout(\n            () => setIsOpenDelayed(true),\n            skipDelayDuration\n          );\n        }\n        onValueChange?.(value2);\n      },\n      defaultProp: defaultValue ?? \"\",\n      caller: NAVIGATION_MENU_NAME\n    });\n    const startCloseTimer = React.useCallback(() => {\n      window.clearTimeout(closeTimerRef.current);\n      closeTimerRef.current = window.setTimeout(() => setValue(\"\"), 150);\n    }, [setValue]);\n    const handleOpen = React.useCallback(\n      (itemValue) => {\n        window.clearTimeout(closeTimerRef.current);\n        setValue(itemValue);\n      },\n      [setValue]\n    );\n    const handleDelayedOpen = React.useCallback(\n      (itemValue) => {\n        const isOpenItem = value === itemValue;\n        if (isOpenItem) {\n          window.clearTimeout(closeTimerRef.current);\n        } else {\n          openTimerRef.current = window.setTimeout(() => {\n            window.clearTimeout(closeTimerRef.current);\n            setValue(itemValue);\n          }, delayDuration);\n        }\n      },\n      [value, setValue, delayDuration]\n    );\n    React.useEffect(() => {\n      return () => {\n        window.clearTimeout(openTimerRef.current);\n        window.clearTimeout(closeTimerRef.current);\n        window.clearTimeout(skipDelayTimerRef.current);\n      };\n    }, []);\n    return /* @__PURE__ */ jsx(\n      NavigationMenuProvider,\n      {\n        scope: __scopeNavigationMenu,\n        isRootMenu: true,\n        value,\n        dir: direction,\n        orientation,\n        rootNavigationMenu: navigationMenu,\n        onTriggerEnter: (itemValue) => {\n          window.clearTimeout(openTimerRef.current);\n          if (isOpenDelayed) handleDelayedOpen(itemValue);\n          else handleOpen(itemValue);\n        },\n        onTriggerLeave: () => {\n          window.clearTimeout(openTimerRef.current);\n          startCloseTimer();\n        },\n        onContentEnter: () => window.clearTimeout(closeTimerRef.current),\n        onContentLeave: startCloseTimer,\n        onItemSelect: (itemValue) => {\n          setValue((prevValue) => prevValue === itemValue ? \"\" : itemValue);\n        },\n        onItemDismiss: () => setValue(\"\"),\n        children: /* @__PURE__ */ jsx(\n          Primitive.nav,\n          {\n            \"aria-label\": \"Main\",\n            \"data-orientation\": orientation,\n            dir: direction,\n            ...NavigationMenuProps,\n            ref: composedRef\n          }\n        )\n      }\n    );\n  }\n);\nNavigationMenu.displayName = NAVIGATION_MENU_NAME;\nvar SUB_NAME = \"NavigationMenuSub\";\nvar NavigationMenuSub = React.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      __scopeNavigationMenu,\n      value: valueProp,\n      onValueChange,\n      defaultValue,\n      orientation = \"horizontal\",\n      ...subProps\n    } = props;\n    const context = useNavigationMenuContext(SUB_NAME, __scopeNavigationMenu);\n    const [value, setValue] = useControllableState({\n      prop: valueProp,\n      onChange: onValueChange,\n      defaultProp: defaultValue ?? \"\",\n      caller: SUB_NAME\n    });\n    return /* @__PURE__ */ jsx(\n      NavigationMenuProvider,\n      {\n        scope: __scopeNavigationMenu,\n        isRootMenu: false,\n        value,\n        dir: context.dir,\n        orientation,\n        rootNavigationMenu: context.rootNavigationMenu,\n        onTriggerEnter: (itemValue) => setValue(itemValue),\n        onItemSelect: (itemValue) => setValue(itemValue),\n        onItemDismiss: () => setValue(\"\"),\n        children: /* @__PURE__ */ jsx(Primitive.div, { \"data-orientation\": orientation, ...subProps, ref: forwardedRef })\n      }\n    );\n  }\n);\nNavigationMenuSub.displayName = SUB_NAME;\nvar NavigationMenuProvider = (props) => {\n  const {\n    scope,\n    isRootMenu,\n    rootNavigationMenu,\n    dir,\n    orientation,\n    children,\n    value,\n    onItemSelect,\n    onItemDismiss,\n    onTriggerEnter,\n    onTriggerLeave,\n    onContentEnter,\n    onContentLeave\n  } = props;\n  const [viewport, setViewport] = React.useState(null);\n  const [viewportContent, setViewportContent] = React.useState(/* @__PURE__ */ new Map());\n  const [indicatorTrack, setIndicatorTrack] = React.useState(null);\n  return /* @__PURE__ */ jsx(\n    NavigationMenuProviderImpl,\n    {\n      scope,\n      isRootMenu,\n      rootNavigationMenu,\n      value,\n      previousValue: usePrevious(value),\n      baseId: useId(),\n      dir,\n      orientation,\n      viewport,\n      onViewportChange: setViewport,\n      indicatorTrack,\n      onIndicatorTrackChange: setIndicatorTrack,\n      onTriggerEnter: useCallbackRef(onTriggerEnter),\n      onTriggerLeave: useCallbackRef(onTriggerLeave),\n      onContentEnter: useCallbackRef(onContentEnter),\n      onContentLeave: useCallbackRef(onContentLeave),\n      onItemSelect: useCallbackRef(onItemSelect),\n      onItemDismiss: useCallbackRef(onItemDismiss),\n      onViewportContentChange: React.useCallback((contentValue, contentData) => {\n        setViewportContent((prevContent) => {\n          prevContent.set(contentValue, contentData);\n          return new Map(prevContent);\n        });\n      }, []),\n      onViewportContentRemove: React.useCallback((contentValue) => {\n        setViewportContent((prevContent) => {\n          if (!prevContent.has(contentValue)) return prevContent;\n          prevContent.delete(contentValue);\n          return new Map(prevContent);\n        });\n      }, []),\n      children: /* @__PURE__ */ jsx(Collection.Provider, { scope, children: /* @__PURE__ */ jsx(ViewportContentProvider, { scope, items: viewportContent, children }) })\n    }\n  );\n};\nvar LIST_NAME = \"NavigationMenuList\";\nvar NavigationMenuList = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeNavigationMenu, ...listProps } = props;\n    const context = useNavigationMenuContext(LIST_NAME, __scopeNavigationMenu);\n    const list = /* @__PURE__ */ jsx(Primitive.ul, { \"data-orientation\": context.orientation, ...listProps, ref: forwardedRef });\n    return /* @__PURE__ */ jsx(Primitive.div, { style: { position: \"relative\" }, ref: context.onIndicatorTrackChange, children: /* @__PURE__ */ jsx(Collection.Slot, { scope: __scopeNavigationMenu, children: context.isRootMenu ? /* @__PURE__ */ jsx(FocusGroup, { asChild: true, children: list }) : list }) });\n  }\n);\nNavigationMenuList.displayName = LIST_NAME;\nvar ITEM_NAME = \"NavigationMenuItem\";\nvar [NavigationMenuItemContextProvider, useNavigationMenuItemContext] = createNavigationMenuContext(ITEM_NAME);\nvar NavigationMenuItem = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeNavigationMenu, value: valueProp, ...itemProps } = props;\n    const autoValue = useId();\n    const value = valueProp || autoValue || \"LEGACY_REACT_AUTO_VALUE\";\n    const contentRef = React.useRef(null);\n    const triggerRef = React.useRef(null);\n    const focusProxyRef = React.useRef(null);\n    const restoreContentTabOrderRef = React.useRef(() => {\n    });\n    const wasEscapeCloseRef = React.useRef(false);\n    const handleContentEntry = React.useCallback((side = \"start\") => {\n      if (contentRef.current) {\n        restoreContentTabOrderRef.current();\n        const candidates = getTabbableCandidates(contentRef.current);\n        if (candidates.length) focusFirst(side === \"start\" ? candidates : candidates.reverse());\n      }\n    }, []);\n    const handleContentExit = React.useCallback(() => {\n      if (contentRef.current) {\n        const candidates = getTabbableCandidates(contentRef.current);\n        if (candidates.length) restoreContentTabOrderRef.current = removeFromTabOrder(candidates);\n      }\n    }, []);\n    return /* @__PURE__ */ jsx(\n      NavigationMenuItemContextProvider,\n      {\n        scope: __scopeNavigationMenu,\n        value,\n        triggerRef,\n        contentRef,\n        focusProxyRef,\n        wasEscapeCloseRef,\n        onEntryKeyDown: handleContentEntry,\n        onFocusProxyEnter: handleContentEntry,\n        onRootContentClose: handleContentExit,\n        onContentFocusOutside: handleContentExit,\n        children: /* @__PURE__ */ jsx(Primitive.li, { ...itemProps, ref: forwardedRef })\n      }\n    );\n  }\n);\nNavigationMenuItem.displayName = ITEM_NAME;\nvar TRIGGER_NAME = \"NavigationMenuTrigger\";\nvar NavigationMenuTrigger = React.forwardRef((props, forwardedRef) => {\n  const { __scopeNavigationMenu, disabled, ...triggerProps } = props;\n  const context = useNavigationMenuContext(TRIGGER_NAME, props.__scopeNavigationMenu);\n  const itemContext = useNavigationMenuItemContext(TRIGGER_NAME, props.__scopeNavigationMenu);\n  const ref = React.useRef(null);\n  const composedRefs = useComposedRefs(ref, itemContext.triggerRef, forwardedRef);\n  const triggerId = makeTriggerId(context.baseId, itemContext.value);\n  const contentId = makeContentId(context.baseId, itemContext.value);\n  const hasPointerMoveOpenedRef = React.useRef(false);\n  const wasClickCloseRef = React.useRef(false);\n  const open = itemContext.value === context.value;\n  return /* @__PURE__ */ jsxs(Fragment, { children: [\n    /* @__PURE__ */ jsx(Collection.ItemSlot, { scope: __scopeNavigationMenu, value: itemContext.value, children: /* @__PURE__ */ jsx(FocusGroupItem, { asChild: true, children: /* @__PURE__ */ jsx(\n      Primitive.button,\n      {\n        id: triggerId,\n        disabled,\n        \"data-disabled\": disabled ? \"\" : void 0,\n        \"data-state\": getOpenState(open),\n        \"aria-expanded\": open,\n        \"aria-controls\": contentId,\n        ...triggerProps,\n        ref: composedRefs,\n        onPointerEnter: composeEventHandlers(props.onPointerEnter, () => {\n          wasClickCloseRef.current = false;\n          itemContext.wasEscapeCloseRef.current = false;\n        }),\n        onPointerMove: composeEventHandlers(\n          props.onPointerMove,\n          whenMouse(() => {\n            if (disabled || wasClickCloseRef.current || itemContext.wasEscapeCloseRef.current || hasPointerMoveOpenedRef.current)\n              return;\n            context.onTriggerEnter(itemContext.value);\n            hasPointerMoveOpenedRef.current = true;\n          })\n        ),\n        onPointerLeave: composeEventHandlers(\n          props.onPointerLeave,\n          whenMouse(() => {\n            if (disabled) return;\n            context.onTriggerLeave();\n            hasPointerMoveOpenedRef.current = false;\n          })\n        ),\n        onClick: composeEventHandlers(props.onClick, () => {\n          context.onItemSelect(itemContext.value);\n          wasClickCloseRef.current = open;\n        }),\n        onKeyDown: composeEventHandlers(props.onKeyDown, (event) => {\n          const verticalEntryKey = context.dir === \"rtl\" ? \"ArrowLeft\" : \"ArrowRight\";\n          const entryKey = { horizontal: \"ArrowDown\", vertical: verticalEntryKey }[context.orientation];\n          if (open && event.key === entryKey) {\n            itemContext.onEntryKeyDown();\n            event.preventDefault();\n          }\n        })\n      }\n    ) }) }),\n    open && /* @__PURE__ */ jsxs(Fragment, { children: [\n      /* @__PURE__ */ jsx(\n        VisuallyHiddenPrimitive.Root,\n        {\n          \"aria-hidden\": true,\n          tabIndex: 0,\n          ref: itemContext.focusProxyRef,\n          onFocus: (event) => {\n            const content = itemContext.contentRef.current;\n            const prevFocusedElement = event.relatedTarget;\n            const wasTriggerFocused = prevFocusedElement === ref.current;\n            const wasFocusFromContent = content?.contains(prevFocusedElement);\n            if (wasTriggerFocused || !wasFocusFromContent) {\n              itemContext.onFocusProxyEnter(wasTriggerFocused ? \"start\" : \"end\");\n            }\n          }\n        }\n      ),\n      context.viewport && /* @__PURE__ */ jsx(\"span\", { \"aria-owns\": contentId })\n    ] })\n  ] });\n});\nNavigationMenuTrigger.displayName = TRIGGER_NAME;\nvar LINK_NAME = \"NavigationMenuLink\";\nvar LINK_SELECT = \"navigationMenu.linkSelect\";\nvar NavigationMenuLink = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeNavigationMenu, active, onSelect, ...linkProps } = props;\n    return /* @__PURE__ */ jsx(FocusGroupItem, { asChild: true, children: /* @__PURE__ */ jsx(\n      Primitive.a,\n      {\n        \"data-active\": active ? \"\" : void 0,\n        \"aria-current\": active ? \"page\" : void 0,\n        ...linkProps,\n        ref: forwardedRef,\n        onClick: composeEventHandlers(\n          props.onClick,\n          (event) => {\n            const target = event.target;\n            const linkSelectEvent = new CustomEvent(LINK_SELECT, {\n              bubbles: true,\n              cancelable: true\n            });\n            target.addEventListener(LINK_SELECT, (event2) => onSelect?.(event2), { once: true });\n            dispatchDiscreteCustomEvent(target, linkSelectEvent);\n            if (!linkSelectEvent.defaultPrevented && !event.metaKey) {\n              const rootContentDismissEvent = new CustomEvent(ROOT_CONTENT_DISMISS, {\n                bubbles: true,\n                cancelable: true\n              });\n              dispatchDiscreteCustomEvent(target, rootContentDismissEvent);\n            }\n          },\n          { checkForDefaultPrevented: false }\n        )\n      }\n    ) });\n  }\n);\nNavigationMenuLink.displayName = LINK_NAME;\nvar INDICATOR_NAME = \"NavigationMenuIndicator\";\nvar NavigationMenuIndicator = React.forwardRef((props, forwardedRef) => {\n  const { forceMount, ...indicatorProps } = props;\n  const context = useNavigationMenuContext(INDICATOR_NAME, props.__scopeNavigationMenu);\n  const isVisible = Boolean(context.value);\n  return context.indicatorTrack ? ReactDOM.createPortal(\n    /* @__PURE__ */ jsx(Presence, { present: forceMount || isVisible, children: /* @__PURE__ */ jsx(NavigationMenuIndicatorImpl, { ...indicatorProps, ref: forwardedRef }) }),\n    context.indicatorTrack\n  ) : null;\n});\nNavigationMenuIndicator.displayName = INDICATOR_NAME;\nvar NavigationMenuIndicatorImpl = React.forwardRef((props, forwardedRef) => {\n  const { __scopeNavigationMenu, ...indicatorProps } = props;\n  const context = useNavigationMenuContext(INDICATOR_NAME, __scopeNavigationMenu);\n  const getItems = useCollection(__scopeNavigationMenu);\n  const [activeTrigger, setActiveTrigger] = React.useState(\n    null\n  );\n  const [position, setPosition] = React.useState(null);\n  const isHorizontal = context.orientation === \"horizontal\";\n  const isVisible = Boolean(context.value);\n  React.useEffect(() => {\n    const items = getItems();\n    const triggerNode = items.find((item) => item.value === context.value)?.ref.current;\n    if (triggerNode) setActiveTrigger(triggerNode);\n  }, [getItems, context.value]);\n  const handlePositionChange = () => {\n    if (activeTrigger) {\n      setPosition({\n        size: isHorizontal ? activeTrigger.offsetWidth : activeTrigger.offsetHeight,\n        offset: isHorizontal ? activeTrigger.offsetLeft : activeTrigger.offsetTop\n      });\n    }\n  };\n  useResizeObserver(activeTrigger, handlePositionChange);\n  useResizeObserver(context.indicatorTrack, handlePositionChange);\n  return position ? /* @__PURE__ */ jsx(\n    Primitive.div,\n    {\n      \"aria-hidden\": true,\n      \"data-state\": isVisible ? \"visible\" : \"hidden\",\n      \"data-orientation\": context.orientation,\n      ...indicatorProps,\n      ref: forwardedRef,\n      style: {\n        position: \"absolute\",\n        ...isHorizontal ? {\n          left: 0,\n          width: position.size + \"px\",\n          transform: `translateX(${position.offset}px)`\n        } : {\n          top: 0,\n          height: position.size + \"px\",\n          transform: `translateY(${position.offset}px)`\n        },\n        ...indicatorProps.style\n      }\n    }\n  ) : null;\n});\nvar CONTENT_NAME = \"NavigationMenuContent\";\nvar NavigationMenuContent = React.forwardRef((props, forwardedRef) => {\n  const { forceMount, ...contentProps } = props;\n  const context = useNavigationMenuContext(CONTENT_NAME, props.__scopeNavigationMenu);\n  const itemContext = useNavigationMenuItemContext(CONTENT_NAME, props.__scopeNavigationMenu);\n  const composedRefs = useComposedRefs(itemContext.contentRef, forwardedRef);\n  const open = itemContext.value === context.value;\n  const commonProps = {\n    value: itemContext.value,\n    triggerRef: itemContext.triggerRef,\n    focusProxyRef: itemContext.focusProxyRef,\n    wasEscapeCloseRef: itemContext.wasEscapeCloseRef,\n    onContentFocusOutside: itemContext.onContentFocusOutside,\n    onRootContentClose: itemContext.onRootContentClose,\n    ...contentProps\n  };\n  return !context.viewport ? /* @__PURE__ */ jsx(Presence, { present: forceMount || open, children: /* @__PURE__ */ jsx(\n    NavigationMenuContentImpl,\n    {\n      \"data-state\": getOpenState(open),\n      ...commonProps,\n      ref: composedRefs,\n      onPointerEnter: composeEventHandlers(props.onPointerEnter, context.onContentEnter),\n      onPointerLeave: composeEventHandlers(\n        props.onPointerLeave,\n        whenMouse(context.onContentLeave)\n      ),\n      style: {\n        // Prevent interaction when animating out\n        pointerEvents: !open && context.isRootMenu ? \"none\" : void 0,\n        ...commonProps.style\n      }\n    }\n  ) }) : /* @__PURE__ */ jsx(ViewportContentMounter, { forceMount, ...commonProps, ref: composedRefs });\n});\nNavigationMenuContent.displayName = CONTENT_NAME;\nvar ViewportContentMounter = React.forwardRef((props, forwardedRef) => {\n  const context = useNavigationMenuContext(CONTENT_NAME, props.__scopeNavigationMenu);\n  const { onViewportContentChange, onViewportContentRemove } = context;\n  useLayoutEffect(() => {\n    onViewportContentChange(props.value, {\n      ref: forwardedRef,\n      ...props\n    });\n  }, [props, forwardedRef, onViewportContentChange]);\n  useLayoutEffect(() => {\n    return () => onViewportContentRemove(props.value);\n  }, [props.value, onViewportContentRemove]);\n  return null;\n});\nvar ROOT_CONTENT_DISMISS = \"navigationMenu.rootContentDismiss\";\nvar NavigationMenuContentImpl = React.forwardRef((props, forwardedRef) => {\n  const {\n    __scopeNavigationMenu,\n    value,\n    triggerRef,\n    focusProxyRef,\n    wasEscapeCloseRef,\n    onRootContentClose,\n    onContentFocusOutside,\n    ...contentProps\n  } = props;\n  const context = useNavigationMenuContext(CONTENT_NAME, __scopeNavigationMenu);\n  const ref = React.useRef(null);\n  const composedRefs = useComposedRefs(ref, forwardedRef);\n  const triggerId = makeTriggerId(context.baseId, value);\n  const contentId = makeContentId(context.baseId, value);\n  const getItems = useCollection(__scopeNavigationMenu);\n  const prevMotionAttributeRef = React.useRef(null);\n  const { onItemDismiss } = context;\n  React.useEffect(() => {\n    const content = ref.current;\n    if (context.isRootMenu && content) {\n      const handleClose = () => {\n        onItemDismiss();\n        onRootContentClose();\n        if (content.contains(document.activeElement)) triggerRef.current?.focus();\n      };\n      content.addEventListener(ROOT_CONTENT_DISMISS, handleClose);\n      return () => content.removeEventListener(ROOT_CONTENT_DISMISS, handleClose);\n    }\n  }, [context.isRootMenu, props.value, triggerRef, onItemDismiss, onRootContentClose]);\n  const motionAttribute = React.useMemo(() => {\n    const items = getItems();\n    const values = items.map((item) => item.value);\n    if (context.dir === \"rtl\") values.reverse();\n    const index = values.indexOf(context.value);\n    const prevIndex = values.indexOf(context.previousValue);\n    const isSelected = value === context.value;\n    const wasSelected = prevIndex === values.indexOf(value);\n    if (!isSelected && !wasSelected) return prevMotionAttributeRef.current;\n    const attribute = (() => {\n      if (index !== prevIndex) {\n        if (isSelected && prevIndex !== -1) return index > prevIndex ? \"from-end\" : \"from-start\";\n        if (wasSelected && index !== -1) return index > prevIndex ? \"to-start\" : \"to-end\";\n      }\n      return null;\n    })();\n    prevMotionAttributeRef.current = attribute;\n    return attribute;\n  }, [context.previousValue, context.value, context.dir, getItems, value]);\n  return /* @__PURE__ */ jsx(FocusGroup, { asChild: true, children: /* @__PURE__ */ jsx(\n    DismissableLayer,\n    {\n      id: contentId,\n      \"aria-labelledby\": triggerId,\n      \"data-motion\": motionAttribute,\n      \"data-orientation\": context.orientation,\n      ...contentProps,\n      ref: composedRefs,\n      disableOutsidePointerEvents: false,\n      onDismiss: () => {\n        const rootContentDismissEvent = new Event(ROOT_CONTENT_DISMISS, {\n          bubbles: true,\n          cancelable: true\n        });\n        ref.current?.dispatchEvent(rootContentDismissEvent);\n      },\n      onFocusOutside: composeEventHandlers(props.onFocusOutside, (event) => {\n        onContentFocusOutside();\n        const target = event.target;\n        if (context.rootNavigationMenu?.contains(target)) event.preventDefault();\n      }),\n      onPointerDownOutside: composeEventHandlers(props.onPointerDownOutside, (event) => {\n        const target = event.target;\n        const isTrigger = getItems().some((item) => item.ref.current?.contains(target));\n        const isRootViewport = context.isRootMenu && context.viewport?.contains(target);\n        if (isTrigger || isRootViewport || !context.isRootMenu) event.preventDefault();\n      }),\n      onKeyDown: composeEventHandlers(props.onKeyDown, (event) => {\n        const isMetaKey = event.altKey || event.ctrlKey || event.metaKey;\n        const isTabKey = event.key === \"Tab\" && !isMetaKey;\n        if (isTabKey) {\n          const candidates = getTabbableCandidates(event.currentTarget);\n          const focusedElement = document.activeElement;\n          const index = candidates.findIndex((candidate) => candidate === focusedElement);\n          const isMovingBackwards = event.shiftKey;\n          const nextCandidates = isMovingBackwards ? candidates.slice(0, index).reverse() : candidates.slice(index + 1, candidates.length);\n          if (focusFirst(nextCandidates)) {\n            event.preventDefault();\n          } else {\n            focusProxyRef.current?.focus();\n          }\n        }\n      }),\n      onEscapeKeyDown: composeEventHandlers(props.onEscapeKeyDown, (_event) => {\n        wasEscapeCloseRef.current = true;\n      })\n    }\n  ) });\n});\nvar VIEWPORT_NAME = \"NavigationMenuViewport\";\nvar NavigationMenuViewport = React.forwardRef((props, forwardedRef) => {\n  const { forceMount, ...viewportProps } = props;\n  const context = useNavigationMenuContext(VIEWPORT_NAME, props.__scopeNavigationMenu);\n  const open = Boolean(context.value);\n  return /* @__PURE__ */ jsx(Presence, { present: forceMount || open, children: /* @__PURE__ */ jsx(NavigationMenuViewportImpl, { ...viewportProps, ref: forwardedRef }) });\n});\nNavigationMenuViewport.displayName = VIEWPORT_NAME;\nvar NavigationMenuViewportImpl = React.forwardRef((props, forwardedRef) => {\n  const { __scopeNavigationMenu, children, ...viewportImplProps } = props;\n  const context = useNavigationMenuContext(VIEWPORT_NAME, __scopeNavigationMenu);\n  const composedRefs = useComposedRefs(forwardedRef, context.onViewportChange);\n  const viewportContentContext = useViewportContentContext(\n    CONTENT_NAME,\n    props.__scopeNavigationMenu\n  );\n  const [size, setSize] = React.useState(null);\n  const [content, setContent] = React.useState(null);\n  const viewportWidth = size ? size?.width + \"px\" : void 0;\n  const viewportHeight = size ? size?.height + \"px\" : void 0;\n  const open = Boolean(context.value);\n  const activeContentValue = open ? context.value : context.previousValue;\n  const handleSizeChange = () => {\n    if (content) setSize({ width: content.offsetWidth, height: content.offsetHeight });\n  };\n  useResizeObserver(content, handleSizeChange);\n  return /* @__PURE__ */ jsx(\n    Primitive.div,\n    {\n      \"data-state\": getOpenState(open),\n      \"data-orientation\": context.orientation,\n      ...viewportImplProps,\n      ref: composedRefs,\n      style: {\n        // Prevent interaction when animating out\n        pointerEvents: !open && context.isRootMenu ? \"none\" : void 0,\n        [\"--radix-navigation-menu-viewport-width\"]: viewportWidth,\n        [\"--radix-navigation-menu-viewport-height\"]: viewportHeight,\n        ...viewportImplProps.style\n      },\n      onPointerEnter: composeEventHandlers(props.onPointerEnter, context.onContentEnter),\n      onPointerLeave: composeEventHandlers(props.onPointerLeave, whenMouse(context.onContentLeave)),\n      children: Array.from(viewportContentContext.items).map(([value, { ref, forceMount, ...props2 }]) => {\n        const isActive = activeContentValue === value;\n        return /* @__PURE__ */ jsx(Presence, { present: forceMount || isActive, children: /* @__PURE__ */ jsx(\n          NavigationMenuContentImpl,\n          {\n            ...props2,\n            ref: composeRefs(ref, (node) => {\n              if (isActive && node) setContent(node);\n            })\n          }\n        ) }, value);\n      })\n    }\n  );\n});\nvar FOCUS_GROUP_NAME = \"FocusGroup\";\nvar FocusGroup = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeNavigationMenu, ...groupProps } = props;\n    const context = useNavigationMenuContext(FOCUS_GROUP_NAME, __scopeNavigationMenu);\n    return /* @__PURE__ */ jsx(FocusGroupCollection.Provider, { scope: __scopeNavigationMenu, children: /* @__PURE__ */ jsx(FocusGroupCollection.Slot, { scope: __scopeNavigationMenu, children: /* @__PURE__ */ jsx(Primitive.div, { dir: context.dir, ...groupProps, ref: forwardedRef }) }) });\n  }\n);\nvar ARROW_KEYS = [\"ArrowRight\", \"ArrowLeft\", \"ArrowUp\", \"ArrowDown\"];\nvar FOCUS_GROUP_ITEM_NAME = \"FocusGroupItem\";\nvar FocusGroupItem = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeNavigationMenu, ...groupProps } = props;\n    const getItems = useFocusGroupCollection(__scopeNavigationMenu);\n    const context = useNavigationMenuContext(FOCUS_GROUP_ITEM_NAME, __scopeNavigationMenu);\n    return /* @__PURE__ */ jsx(FocusGroupCollection.ItemSlot, { scope: __scopeNavigationMenu, children: /* @__PURE__ */ jsx(\n      Primitive.button,\n      {\n        ...groupProps,\n        ref: forwardedRef,\n        onKeyDown: composeEventHandlers(props.onKeyDown, (event) => {\n          const isFocusNavigationKey = [\"Home\", \"End\", ...ARROW_KEYS].includes(event.key);\n          if (isFocusNavigationKey) {\n            let candidateNodes = getItems().map((item) => item.ref.current);\n            const prevItemKey = context.dir === \"rtl\" ? \"ArrowRight\" : \"ArrowLeft\";\n            const prevKeys = [prevItemKey, \"ArrowUp\", \"End\"];\n            if (prevKeys.includes(event.key)) candidateNodes.reverse();\n            if (ARROW_KEYS.includes(event.key)) {\n              const currentIndex = candidateNodes.indexOf(event.currentTarget);\n              candidateNodes = candidateNodes.slice(currentIndex + 1);\n            }\n            setTimeout(() => focusFirst(candidateNodes));\n            event.preventDefault();\n          }\n        })\n      }\n    ) });\n  }\n);\nfunction getTabbableCandidates(container) {\n  const nodes = [];\n  const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n    acceptNode: (node) => {\n      const isHiddenInput = node.tagName === \"INPUT\" && node.type === \"hidden\";\n      if (node.disabled || node.hidden || isHiddenInput) return NodeFilter.FILTER_SKIP;\n      return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n    }\n  });\n  while (walker.nextNode()) nodes.push(walker.currentNode);\n  return nodes;\n}\nfunction focusFirst(candidates) {\n  const previouslyFocusedElement = document.activeElement;\n  return candidates.some((candidate) => {\n    if (candidate === previouslyFocusedElement) return true;\n    candidate.focus();\n    return document.activeElement !== previouslyFocusedElement;\n  });\n}\nfunction removeFromTabOrder(candidates) {\n  candidates.forEach((candidate) => {\n    candidate.dataset.tabindex = candidate.getAttribute(\"tabindex\") || \"\";\n    candidate.setAttribute(\"tabindex\", \"-1\");\n  });\n  return () => {\n    candidates.forEach((candidate) => {\n      const prevTabIndex = candidate.dataset.tabindex;\n      candidate.setAttribute(\"tabindex\", prevTabIndex);\n    });\n  };\n}\nfunction useResizeObserver(element, onResize) {\n  const handleResize = useCallbackRef(onResize);\n  useLayoutEffect(() => {\n    let rAF = 0;\n    if (element) {\n      const resizeObserver = new ResizeObserver(() => {\n        cancelAnimationFrame(rAF);\n        rAF = window.requestAnimationFrame(handleResize);\n      });\n      resizeObserver.observe(element);\n      return () => {\n        window.cancelAnimationFrame(rAF);\n        resizeObserver.unobserve(element);\n      };\n    }\n  }, [element, handleResize]);\n}\nfunction getOpenState(open) {\n  return open ? \"open\" : \"closed\";\n}\nfunction makeTriggerId(baseId, value) {\n  return `${baseId}-trigger-${value}`;\n}\nfunction makeContentId(baseId, value) {\n  return `${baseId}-content-${value}`;\n}\nfunction whenMouse(handler) {\n  return (event) => event.pointerType === \"mouse\" ? handler(event) : void 0;\n}\nvar Root2 = NavigationMenu;\nvar Sub = NavigationMenuSub;\nvar List = NavigationMenuList;\nvar Item = NavigationMenuItem;\nvar Trigger = NavigationMenuTrigger;\nvar Link = NavigationMenuLink;\nvar Indicator = NavigationMenuIndicator;\nvar Content = NavigationMenuContent;\nvar Viewport = NavigationMenuViewport;\nexport {\n  Content,\n  Indicator,\n  Item,\n  Link,\n  List,\n  NavigationMenu,\n  NavigationMenuContent,\n  NavigationMenuIndicator,\n  NavigationMenuItem,\n  NavigationMenuLink,\n  NavigationMenuList,\n  NavigationMenuSub,\n  NavigationMenuTrigger,\n  NavigationMenuViewport,\n  Root2 as Root,\n  Sub,\n  Trigger,\n  Viewport,\n  createNavigationMenuScope\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/use-previous/src/use-previous.tsx\nimport * as React from \"react\";\nfunction usePrevious(value) {\n  const ref = React.useRef({ value, previous: value });\n  return React.useMemo(() => {\n    if (ref.current.value !== value) {\n      ref.current.previous = ref.current.value;\n      ref.current.value = value;\n    }\n    return ref.current.previous;\n  }, [value]);\n}\nexport {\n  usePrevious\n};\n//# sourceMappingURL=index.mjs.map\n", "/**\n * Copyright 2022 Joe Bell. All rights reserved.\n *\n * This file is licensed to you under the Apache License, Version 2.0\n * (the \"License\"); you may not use this file except in compliance with the\n * License. You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS, WITHOUT\n * WARRANTIES OR REPRESENTATIONS OF ANY KIND, either express or implied. See the\n * License for the specific language governing permissions and limitations under\n * the License.\n */ import { clsx } from \"clsx\";\nconst falsyToString = (value)=>typeof value === \"boolean\" ? `${value}` : value === 0 ? \"0\" : value;\nexport const cx = clsx;\nexport const cva = (base, config)=>(props)=>{\n        var _config_compoundVariants;\n        if ((config === null || config === void 0 ? void 0 : config.variants) == null) return cx(base, props === null || props === void 0 ? void 0 : props.class, props === null || props === void 0 ? void 0 : props.className);\n        const { variants, defaultVariants } = config;\n        const getVariantClassNames = Object.keys(variants).map((variant)=>{\n            const variantProp = props === null || props === void 0 ? void 0 : props[variant];\n            const defaultVariantProp = defaultVariants === null || defaultVariants === void 0 ? void 0 : defaultVariants[variant];\n            if (variantProp === null) return null;\n            const variantKey = falsyToString(variantProp) || falsyToString(defaultVariantProp);\n            return variants[variant][variantKey];\n        });\n        const propsWithoutUndefined = props && Object.entries(props).reduce((acc, param)=>{\n            let [key, value] = param;\n            if (value === undefined) {\n                return acc;\n            }\n            acc[key] = value;\n            return acc;\n        }, {});\n        const getCompoundVariantClassNames = config === null || config === void 0 ? void 0 : (_config_compoundVariants = config.compoundVariants) === null || _config_compoundVariants === void 0 ? void 0 : _config_compoundVariants.reduce((acc, param)=>{\n            let { class: cvClass, className: cvClassName, ...compoundVariantOptions } = param;\n            return Object.entries(compoundVariantOptions).every((param)=>{\n                let [key, value] = param;\n                return Array.isArray(value) ? value.includes({\n                    ...defaultVariants,\n                    ...propsWithoutUndefined\n                }[key]) : ({\n                    ...defaultVariants,\n                    ...propsWithoutUndefined\n                })[key] === value;\n            }) ? [\n                ...acc,\n                cvClass,\n                cvClassName\n            ] : acc;\n        }, []);\n        return cx(base, getVariantClassNames, getCompoundVariantClassNames, props === null || props === void 0 ? void 0 : props.class, props === null || props === void 0 ? void 0 : props.className);\n    };\n\n", "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport { forwardRef, createElement } from 'react';\nimport defaultAttributes from './defaultAttributes.js';\nimport { mergeClasses, hasA11yProp } from './shared/src/utils.js';\n\nconst Icon = forwardRef(\n  ({\n    color = \"currentColor\",\n    size = 24,\n    strokeWidth = 2,\n    absoluteStrokeWidth,\n    className = \"\",\n    children,\n    iconNode,\n    ...rest\n  }, ref) => createElement(\n    \"svg\",\n    {\n      ref,\n      ...defaultAttributes,\n      width: size,\n      height: size,\n      stroke: color,\n      strokeWidth: absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,\n      className: mergeClasses(\"lucide\", className),\n      ...!children && !hasA11yProp(rest) && { \"aria-hidden\": \"true\" },\n      ...rest\n    },\n    [\n      ...iconNode.map(([tag, attrs]) => createElement(tag, attrs)),\n      ...Array.isArray(children) ? children : [children]\n    ]\n  )\n);\n\nexport { Icon as default };\n//# sourceMappingURL=Icon.js.map\n", "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport { forwardRef, createElement } from 'react';\nimport { mergeClasses, toKebabCase, toPascalCase } from './shared/src/utils.js';\nimport Icon from './Icon.js';\n\nconst createLucideIcon = (iconName, iconNode) => {\n  const Component = forwardRef(\n    ({ className, ...props }, ref) => createElement(Icon, {\n      ref,\n      iconNode,\n      className: mergeClasses(\n        `lucide-${toKebabCase(toPascalCase(iconName))}`,\n        `lucide-${iconName}`,\n        className\n      ),\n      ...props\n    })\n  );\n  Component.displayName = toPascalCase(iconName);\n  return Component;\n};\n\nexport { createLucideIcon as default };\n//# sourceMappingURL=createLucideIcon.js.map\n", "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nvar defaultAttributes = {\n  xmlns: \"http://www.w3.org/2000/svg\",\n  width: 24,\n  height: 24,\n  viewBox: \"0 0 24 24\",\n  fill: \"none\",\n  stroke: \"currentColor\",\n  strokeWidth: 2,\n  strokeLinecap: \"round\",\n  strokeLinejoin: \"round\"\n};\n\nexport { defaultAttributes as default };\n//# sourceMappingURL=defaultAttributes.js.map\n", "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [[\"path\", { d: \"m6 9 6 6 6-6\", key: \"qrunsl\" }]];\nconst ChevronDown = createLucideIcon(\"chevron-down\", __iconNode);\n\nexport { __iconNode, ChevronDown as default };\n//# sourceMappingURL=chevron-down.js.map\n", "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nconst toKebabCase = (string) => string.replace(/([a-z0-9])([A-Z])/g, \"$1-$2\").toLowerCase();\nconst toCamelCase = (string) => string.replace(\n  /^([A-Z])|[\\s-_]+(\\w)/g,\n  (match, p1, p2) => p2 ? p2.toUpperCase() : p1.toLowerCase()\n);\nconst toPascalCase = (string) => {\n  const camelCase = toCamelCase(string);\n  return camelCase.charAt(0).toUpperCase() + camelCase.slice(1);\n};\nconst mergeClasses = (...classes) => classes.filter((className, index, array) => {\n  return Boolean(className) && className.trim() !== \"\" && array.indexOf(className) === index;\n}).join(\" \").trim();\nconst hasA11yProp = (props) => {\n  for (const prop in props) {\n    if (prop.startsWith(\"aria-\") || prop === \"role\" || prop === \"title\") {\n      return true;\n    }\n  }\n};\n\nexport { hasA11yProp, mergeClasses, toCamelCase, toKebabCase, toPascalCase };\n//# sourceMappingURL=utils.js.map\n"], "names": [], "sourceRoot": ""}