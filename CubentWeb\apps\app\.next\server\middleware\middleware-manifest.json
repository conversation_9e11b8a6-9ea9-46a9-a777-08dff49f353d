{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_ec43db50._.js", "server/edge/chunks/eec21_@clerk_shared_dist_40b2e982._.js", "server/edge/chunks/c67f4_@clerk_backend_dist_d8cc056d._.js", "server/edge/chunks/25c57_@clerk_nextjs_dist_esm_1ca17405._.js", "server/edge/chunks/ec4b9_zod_dist_esm_cbcb71bd._.js", "server/edge/chunks/node_modules__pnpm_16b65189._.js", "server/edge/chunks/[root-of-the-server]__6292175d._.js", "server/edge/chunks/apps_app_edge-wrapper_0144a8dc.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*){(\\\\.json)}?", "originalSource": "/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/(api|trpc)(.*){(\\\\.json)}?", "originalSource": "/(api|trpc)(.*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "W2GDmhqWAEvlNHlwGITKkVIGi0ZjBphzOJ0dTpjoqGM=", "__NEXT_PREVIEW_MODE_ID": "6e3c0fe37e0c9c26a37c492f819d49f9", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "d501f9b9ec2237b7f40f4c9ceb5e98aa3372af41aaa6be529a2e5988910a4b3e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "636d18bef2bae2ca1b9ed4986df426d2de1afe28985f98306e5fc716b17d4307"}}}, "instrumentation": null, "functions": {}}