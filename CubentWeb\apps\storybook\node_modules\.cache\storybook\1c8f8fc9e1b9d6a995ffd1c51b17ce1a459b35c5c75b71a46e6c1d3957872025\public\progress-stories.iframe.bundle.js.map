{"version": 3, "file": "progress-stories.iframe.bundle.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;;;;;;;;;;;;;;;;;;;;;;;ACpGA;;AAEA;AACA;AAEA;AAEA;AAKA;AAEA;AACA;AAIA;AAEA;AACA;AACA;AACA;AAAA;AAAA;;;;;;;;;;;AAIA;AArBA;AAuBA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5BA;AAEA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AAIA;;;AAGA;AAEA;;;AAGA;AACA;AACA;AACA;AACA;AAEA;;;AAGA;AACA;AACA;AACA;AACA", "sources": ["webpack://storybook/../../node_modules/.pnpm/@radix-ui+react-progress@1._e9f77b710011a4f75a12cc3a54e0b06d/node_modules/@radix-ui/react-progress/dist/index.mjs", "webpack://storybook/../../packages/design-system/components/ui/progress.tsx", "webpack://storybook/./stories/progress.stories.tsx"], "sourcesContent": ["\"use client\";\n\n// src/progress.tsx\nimport * as React from \"react\";\nimport { createContextScope } from \"@radix-ui/react-context\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { jsx } from \"react/jsx-runtime\";\nvar PROGRESS_NAME = \"Progress\";\nvar DEFAULT_MAX = 100;\nvar [createProgressContext, createProgressScope] = createContextScope(PROGRESS_NAME);\nvar [ProgressProvider, useProgressContext] = createProgressContext(PROGRESS_NAME);\nvar Progress = React.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      __scopeProgress,\n      value: valueProp = null,\n      max: maxProp,\n      getValueLabel = defaultGetValueLabel,\n      ...progressProps\n    } = props;\n    if ((maxProp || maxProp === 0) && !isValidMaxNumber(maxProp)) {\n      console.error(getInvalidMaxError(`${maxProp}`, \"Progress\"));\n    }\n    const max = isValidMaxNumber(maxProp) ? maxProp : DEFAULT_MAX;\n    if (valueProp !== null && !isValidValueNumber(valueProp, max)) {\n      console.error(getInvalidValueError(`${valueProp}`, \"Progress\"));\n    }\n    const value = isValidValueNumber(valueProp, max) ? valueProp : null;\n    const valueLabel = isNumber(value) ? getValueLabel(value, max) : void 0;\n    return /* @__PURE__ */ jsx(ProgressProvider, { scope: __scopeProgress, value, max, children: /* @__PURE__ */ jsx(\n      Primitive.div,\n      {\n        \"aria-valuemax\": max,\n        \"aria-valuemin\": 0,\n        \"aria-valuenow\": isNumber(value) ? value : void 0,\n        \"aria-valuetext\": valueLabel,\n        role: \"progressbar\",\n        \"data-state\": getProgressState(value, max),\n        \"data-value\": value ?? void 0,\n        \"data-max\": max,\n        ...progressProps,\n        ref: forwardedRef\n      }\n    ) });\n  }\n);\nProgress.displayName = PROGRESS_NAME;\nvar INDICATOR_NAME = \"ProgressIndicator\";\nvar ProgressIndicator = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeProgress, ...indicatorProps } = props;\n    const context = useProgressContext(INDICATOR_NAME, __scopeProgress);\n    return /* @__PURE__ */ jsx(\n      Primitive.div,\n      {\n        \"data-state\": getProgressState(context.value, context.max),\n        \"data-value\": context.value ?? void 0,\n        \"data-max\": context.max,\n        ...indicatorProps,\n        ref: forwardedRef\n      }\n    );\n  }\n);\nProgressIndicator.displayName = INDICATOR_NAME;\nfunction defaultGetValueLabel(value, max) {\n  return `${Math.round(value / max * 100)}%`;\n}\nfunction getProgressState(value, maxValue) {\n  return value == null ? \"indeterminate\" : value === maxValue ? \"complete\" : \"loading\";\n}\nfunction isNumber(value) {\n  return typeof value === \"number\";\n}\nfunction isValidMaxNumber(max) {\n  return isNumber(max) && !isNaN(max) && max > 0;\n}\nfunction isValidValueNumber(value, max) {\n  return isNumber(value) && !isNaN(value) && value <= max && value >= 0;\n}\nfunction getInvalidMaxError(propValue, componentName) {\n  return `Invalid prop \\`max\\` of value \\`${propValue}\\` supplied to \\`${componentName}\\`. Only numbers greater than 0 are valid max values. Defaulting to \\`${DEFAULT_MAX}\\`.`;\n}\nfunction getInvalidValueError(propValue, componentName) {\n  return `Invalid prop \\`value\\` of value \\`${propValue}\\` supplied to \\`${componentName}\\`. The \\`value\\` prop must be:\n  - a positive number\n  - less than the value passed to \\`max\\` (or ${DEFAULT_MAX} if no \\`max\\` prop is set)\n  - \\`null\\` or \\`undefined\\` if the progress is indeterminate.\n\nDefaulting to \\`null\\`.`;\n}\nvar Root = Progress;\nvar Indicator = ProgressIndicator;\nexport {\n  Indicator,\n  Progress,\n  ProgressIndicator,\n  Root,\n  createProgressScope\n};\n//# sourceMappingURL=index.mjs.map\n", "\"use client\"\n\nimport * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\n\nimport { cn } from \"@repo/design-system/lib/utils\"\n\nfunction Progress({\n  className,\n  value,\n  ...props\n}: React.ComponentProps<typeof ProgressPrimitive.Root>) {\n  return (\n    <ProgressPrimitive.Root\n      data-slot=\"progress\"\n      className={cn(\n        \"bg-primary/20 relative h-2 w-full overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    >\n      <ProgressPrimitive.Indicator\n        data-slot=\"progress-indicator\"\n        className=\"bg-primary h-full w-full flex-1 transition-all\"\n        style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n      />\n    </ProgressPrimitive.Root>\n  )\n}\n\nexport { Progress }\n", "import type { Meta, StoryObj } from '@storybook/react';\n\nimport { Progress } from '@repo/design-system/components/ui/progress';\n\n/**\n * Displays an indicator showing the completion progress of a task, typically\n * displayed as a progress bar.\n */\nconst meta = {\n  title: 'ui/Progress',\n  component: Progress,\n  tags: ['autodocs'],\n  argTypes: {},\n  args: {\n    value: 30,\n    max: 100,\n  },\n} satisfies Meta<typeof Progress>;\n\nexport default meta;\n\ntype Story = StoryObj<typeof meta>;\n\n/**\n * The default form of the progress.\n */\nexport const Default: Story = {};\n\n/**\n * When the progress is indeterminate.\n */\nexport const Indeterminate: Story = {\n  args: {\n    value: undefined,\n  },\n};\n\n/**\n * When the progress is completed.\n */\nexport const Completed: Story = {\n  args: {\n    value: 100,\n  },\n};\n"], "names": [], "sourceRoot": ""}