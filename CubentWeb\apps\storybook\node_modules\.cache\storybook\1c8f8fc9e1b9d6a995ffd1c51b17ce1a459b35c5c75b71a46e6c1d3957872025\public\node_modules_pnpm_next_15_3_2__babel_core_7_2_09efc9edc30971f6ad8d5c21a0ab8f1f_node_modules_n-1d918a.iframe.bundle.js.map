{"version": 3, "file": "node_modules_pnpm_next_15_3_2__babel_core_7_2_09efc9edc30971f6ad8d5c21a0ab8f1f_node_modules_n-1d918a.iframe.bundle.js", "mappings": ";;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "sources": ["webpack://storybook/../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react-dom/cjs/react-dom-test-utils.production.js"], "sourcesContent": ["/**\n * @license React\n * react-dom-test-utils.production.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\nvar React = require(\"next/dist/compiled/react\"),\n  didWarnAboutUsingAct = !1;\nexports.act = function (callback) {\n  !1 === didWarnAboutUsingAct &&\n    ((didWarnAboutUsingAct = !0),\n    console.error(\n      \"`ReactDOMTestUtils.act` is deprecated in favor of `React.act`. Import `act` from `react` instead of `react-dom/test-utils`. See https://react.dev/warnings/react-dom-test-utils for more info.\"\n    ));\n  return React.act(callback);\n};\n"], "names": [], "sourceRoot": ""}