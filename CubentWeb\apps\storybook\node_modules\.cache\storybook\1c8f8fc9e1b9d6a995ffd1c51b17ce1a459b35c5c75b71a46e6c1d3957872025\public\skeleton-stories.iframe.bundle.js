"use strict";
(self["webpackChunkstorybook"] = self["webpackChunkstorybook"] || []).push([["skeleton-stories"],{

/***/ "../../packages/design-system/components/ui/skeleton.tsx":
/*!***************************************************************!*\
  !*** ../../packages/design-system/components/ui/skeleton.tsx ***!
  \***************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Skeleton: () => (/* binding */ Skeleton)
/* harmony export */ });
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/jsx-dev-runtime.js");
/* harmony import */ var _repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @repo/design-system/lib/utils */ "../../packages/design-system/lib/utils.ts");
/* provided dependency */ var __react_refresh_utils__ = __webpack_require__(/*! ../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js */ "../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js");
__webpack_require__.$Refresh$.runtime = __webpack_require__(/*! ../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js */ "../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js");



function Skeleton({ className, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
        "data-slot": "skeleton",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)("bg-accent animate-pulse rounded-md", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\packages\\design-system\\components\\ui\\skeleton.tsx",
        lineNumber: 5,
        columnNumber: 5
    }, this);
}
_c = Skeleton;

Skeleton.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "Skeleton"
};
var _c;
__webpack_require__.$Refresh$.register(_c, "Skeleton");


const $ReactRefreshModuleId$ = __webpack_require__.$Refresh$.moduleId;
const $ReactRefreshCurrentExports$ = __react_refresh_utils__.getModuleExports(
	$ReactRefreshModuleId$
);

function $ReactRefreshModuleRuntime$(exports) {
	if (true) {
		let errorOverlay;
		if (true) {
			errorOverlay = false;
		}
		let testMode;
		if (typeof __react_refresh_test__ !== 'undefined') {
			testMode = __react_refresh_test__;
		}
		return __react_refresh_utils__.executeRuntime(
			exports,
			$ReactRefreshModuleId$,
			module.hot,
			errorOverlay,
			testMode
		);
	}
}

if (typeof Promise !== 'undefined' && $ReactRefreshCurrentExports$ instanceof Promise) {
	$ReactRefreshCurrentExports$.then($ReactRefreshModuleRuntime$);
} else {
	$ReactRefreshModuleRuntime$($ReactRefreshCurrentExports$);
}

/***/ }),

/***/ "./stories/skeleton.stories.tsx":
/*!**************************************!*\
  !*** ./stories/skeleton.stories.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Default: () => (/* binding */ Default),
/* harmony export */   __namedExportsOrder: () => (/* binding */ __namedExportsOrder),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/jsx-dev-runtime.js");
/* harmony import */ var _repo_design_system_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @repo/design-system/components/ui/skeleton */ "../../packages/design-system/components/ui/skeleton.tsx");
/* provided dependency */ var __react_refresh_utils__ = __webpack_require__(/*! ../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js */ "../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js");
__webpack_require__.$Refresh$.runtime = __webpack_require__(/*! ../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js */ "../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js");



/**
 * Use to show a placeholder while content is loading.
 */
const meta = {
  title: 'ui/Skeleton',
  component: _repo_design_system_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__.Skeleton,
  tags: ['autodocs'],
  argTypes: {},
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: "Use to show a placeholder while content is loading."
      }
    }
  }
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (meta);
/**
 * The default form of the skeleton.
 */
const Default = {
  render: args => /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
    className: "flex items-center space-x-4",
    children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__.Skeleton, {
      ...args,
      className: "h-12 w-12 rounded-full"
    }, void 0, false, {
      fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\skeleton.stories.tsx",
      lineNumber: 28,
      columnNumber: 7
    }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
      className: "space-y-2",
      children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__.Skeleton, {
        ...args,
        className: "h-4 w-[250px]"
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\skeleton.stories.tsx",
        lineNumber: 30,
        columnNumber: 9
      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__.Skeleton, {
        ...args,
        className: "h-4 w-[200px]"
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\skeleton.stories.tsx",
        lineNumber: 31,
        columnNumber: 9
      }, undefined)]
    }, void 0, true, {
      fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\skeleton.stories.tsx",
      lineNumber: 29,
      columnNumber: 7
    }, undefined)]
  }, void 0, true, {
    fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\skeleton.stories.tsx",
    lineNumber: 27,
    columnNumber: 5
  }, undefined)
};
;
const __namedExportsOrder = ["Default"];
Default.parameters = {
  ...Default.parameters,
  docs: {
    ...Default.parameters?.docs,
    source: {
      originalSource: "{\n  render: args => <div className=\"flex items-center space-x-4\">\r\n      <Skeleton {...args} className=\"h-12 w-12 rounded-full\" />\r\n      <div className=\"space-y-2\">\r\n        <Skeleton {...args} className=\"h-4 w-[250px]\" />\r\n        <Skeleton {...args} className=\"h-4 w-[200px]\" />\r\n      </div>\r\n    </div>\n}",
      ...Default.parameters?.docs?.source
    },
    description: {
      story: "The default form of the skeleton.",
      ...Default.parameters?.docs?.description
    }
  }
};

const $ReactRefreshModuleId$ = __webpack_require__.$Refresh$.moduleId;
const $ReactRefreshCurrentExports$ = __react_refresh_utils__.getModuleExports(
	$ReactRefreshModuleId$
);

function $ReactRefreshModuleRuntime$(exports) {
	if (true) {
		let errorOverlay;
		if (true) {
			errorOverlay = false;
		}
		let testMode;
		if (typeof __react_refresh_test__ !== 'undefined') {
			testMode = __react_refresh_test__;
		}
		return __react_refresh_utils__.executeRuntime(
			exports,
			$ReactRefreshModuleId$,
			module.hot,
			errorOverlay,
			testMode
		);
	}
}

if (typeof Promise !== 'undefined' && $ReactRefreshCurrentExports$ instanceof Promise) {
	$ReactRefreshCurrentExports$.then($ReactRefreshModuleRuntime$);
} else {
	$ReactRefreshModuleRuntime$($ReactRefreshCurrentExports$);
}

/***/ })

}]);
//# sourceMappingURL=skeleton-stories.iframe.bundle.js.map