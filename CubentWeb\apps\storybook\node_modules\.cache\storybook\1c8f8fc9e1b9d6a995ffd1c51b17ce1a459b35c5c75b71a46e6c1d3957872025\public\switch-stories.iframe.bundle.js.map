{"version": 3, "file": "switch-stories.iframe.bundle.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;;;;;;;;;;;;;;;;;AC/JA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;;;;;;;;;;;;;;;;;;;;;;;ACfA;;AAEA;AACA;AAEA;AAEA;AAIA;AAEA;AACA;AAIA;AAEA;AACA;AACA;;;;;;;;;;;AAMA;AArBA;AAuBA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5BA;AAEA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;;;;;AACA;AAAA;AAAA;AAAA;;;;;;;;;;;AAKA;AAEA;AAIA;;;AAGA;AACA;AACA;AACA;AACA;AAEA;;;AAGA;AACA;AACA;AACA;AACA;AACA", "sources": ["webpack://storybook/../../node_modules/.pnpm/@radix-ui+react-switch@1.2._83d3d54937516a1e4ceff96b7f412053/node_modules/@radix-ui/react-switch/dist/index.mjs", "webpack://storybook/../../node_modules/.pnpm/@radix-ui+react-use-previou_791288859aab8756064fe392350c2e0c/node_modules/@radix-ui/react-use-previous/dist/index.mjs", "webpack://storybook/../../packages/design-system/components/ui/switch.tsx", "webpack://storybook/./stories/switch.stories.tsx"], "sourcesContent": ["\"use client\";\n\n// src/switch.tsx\nimport * as React from \"react\";\nimport { composeEventHandlers } from \"@radix-ui/primitive\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { createContextScope } from \"@radix-ui/react-context\";\nimport { useControllableState } from \"@radix-ui/react-use-controllable-state\";\nimport { usePrevious } from \"@radix-ui/react-use-previous\";\nimport { useSize } from \"@radix-ui/react-use-size\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar SWITCH_NAME = \"Switch\";\nvar [createSwitchContext, createSwitchScope] = createContextScope(SWITCH_NAME);\nvar [SwitchProvider, useSwitchContext] = createSwitchContext(SWITCH_NAME);\nvar Switch = React.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      __scopeSwitch,\n      name,\n      checked: checkedProp,\n      defaultChecked,\n      required,\n      disabled,\n      value = \"on\",\n      onCheckedChange,\n      form,\n      ...switchProps\n    } = props;\n    const [button, setButton] = React.useState(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setButton(node));\n    const hasConsumerStoppedPropagationRef = React.useRef(false);\n    const isFormControl = button ? form || !!button.closest(\"form\") : true;\n    const [checked, setChecked] = useControllableState({\n      prop: checkedProp,\n      defaultProp: defaultChecked ?? false,\n      onChange: onCheckedChange,\n      caller: SWITCH_NAME\n    });\n    return /* @__PURE__ */ jsxs(SwitchProvider, { scope: __scopeSwitch, checked, disabled, children: [\n      /* @__PURE__ */ jsx(\n        Primitive.button,\n        {\n          type: \"button\",\n          role: \"switch\",\n          \"aria-checked\": checked,\n          \"aria-required\": required,\n          \"data-state\": getState(checked),\n          \"data-disabled\": disabled ? \"\" : void 0,\n          disabled,\n          value,\n          ...switchProps,\n          ref: composedRefs,\n          onClick: composeEventHandlers(props.onClick, (event) => {\n            setChecked((prevChecked) => !prevChecked);\n            if (isFormControl) {\n              hasConsumerStoppedPropagationRef.current = event.isPropagationStopped();\n              if (!hasConsumerStoppedPropagationRef.current) event.stopPropagation();\n            }\n          })\n        }\n      ),\n      isFormControl && /* @__PURE__ */ jsx(\n        SwitchBubbleInput,\n        {\n          control: button,\n          bubbles: !hasConsumerStoppedPropagationRef.current,\n          name,\n          value,\n          checked,\n          required,\n          disabled,\n          form,\n          style: { transform: \"translateX(-100%)\" }\n        }\n      )\n    ] });\n  }\n);\nSwitch.displayName = SWITCH_NAME;\nvar THUMB_NAME = \"SwitchThumb\";\nvar SwitchThumb = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeSwitch, ...thumbProps } = props;\n    const context = useSwitchContext(THUMB_NAME, __scopeSwitch);\n    return /* @__PURE__ */ jsx(\n      Primitive.span,\n      {\n        \"data-state\": getState(context.checked),\n        \"data-disabled\": context.disabled ? \"\" : void 0,\n        ...thumbProps,\n        ref: forwardedRef\n      }\n    );\n  }\n);\nSwitchThumb.displayName = THUMB_NAME;\nvar BUBBLE_INPUT_NAME = \"SwitchBubbleInput\";\nvar SwitchBubbleInput = React.forwardRef(\n  ({\n    __scopeSwitch,\n    control,\n    checked,\n    bubbles = true,\n    ...props\n  }, forwardedRef) => {\n    const ref = React.useRef(null);\n    const composedRefs = useComposedRefs(ref, forwardedRef);\n    const prevChecked = usePrevious(checked);\n    const controlSize = useSize(control);\n    React.useEffect(() => {\n      const input = ref.current;\n      if (!input) return;\n      const inputProto = window.HTMLInputElement.prototype;\n      const descriptor = Object.getOwnPropertyDescriptor(\n        inputProto,\n        \"checked\"\n      );\n      const setChecked = descriptor.set;\n      if (prevChecked !== checked && setChecked) {\n        const event = new Event(\"click\", { bubbles });\n        setChecked.call(input, checked);\n        input.dispatchEvent(event);\n      }\n    }, [prevChecked, checked, bubbles]);\n    return /* @__PURE__ */ jsx(\n      \"input\",\n      {\n        type: \"checkbox\",\n        \"aria-hidden\": true,\n        defaultChecked: checked,\n        ...props,\n        tabIndex: -1,\n        ref: composedRefs,\n        style: {\n          ...props.style,\n          ...controlSize,\n          position: \"absolute\",\n          pointerEvents: \"none\",\n          opacity: 0,\n          margin: 0\n        }\n      }\n    );\n  }\n);\nSwitchBubbleInput.displayName = BUBBLE_INPUT_NAME;\nfunction getState(checked) {\n  return checked ? \"checked\" : \"unchecked\";\n}\nvar Root = Switch;\nvar Thumb = SwitchThumb;\nexport {\n  Root,\n  Switch,\n  SwitchThumb,\n  Thumb,\n  createSwitchScope\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/use-previous/src/use-previous.tsx\nimport * as React from \"react\";\nfunction usePrevious(value) {\n  const ref = React.useRef({ value, previous: value });\n  return React.useMemo(() => {\n    if (ref.current.value !== value) {\n      ref.current.previous = ref.current.value;\n      ref.current.value = value;\n    }\n    return ref.current.previous;\n  }, [value]);\n}\nexport {\n  usePrevious\n};\n//# sourceMappingURL=index.mjs.map\n", "\"use client\"\n\nimport * as React from \"react\"\nimport * as SwitchPrimitive from \"@radix-ui/react-switch\"\n\nimport { cn } from \"@repo/design-system/lib/utils\"\n\nfunction Switch({\n  className,\n  ...props\n}: React.ComponentProps<typeof SwitchPrimitive.Root>) {\n  return (\n    <SwitchPrimitive.Root\n      data-slot=\"switch\"\n      className={cn(\n        \"peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    >\n      <SwitchPrimitive.Thumb\n        data-slot=\"switch-thumb\"\n        className={cn(\n          \"bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0\"\n        )}\n      />\n    </SwitchPrimitive.Root>\n  )\n}\n\nexport { Switch }\n", "import type { Meta, StoryObj } from '@storybook/react';\n\nimport { Switch } from '@repo/design-system/components/ui/switch';\n\n/**\n * A control that allows the user to toggle between checked and not checked.\n */\nconst meta = {\n  title: 'ui/Switch',\n  component: Switch,\n  tags: ['autodocs'],\n  argTypes: {},\n  parameters: {\n    layout: 'centered',\n  },\n  render: (args) => (\n    <div className=\"flex items-center space-x-2\">\n      <Switch {...args} />\n      <label htmlFor={args.id} className=\"peer-disabled:text-foreground/50\">\n        Airplane Mode\n      </label>\n    </div>\n  ),\n} satisfies Meta<typeof Switch>;\n\nexport default meta;\n\ntype Story = StoryObj<typeof meta>;\n\n/**\n * The default form of the switch.\n */\nexport const Default: Story = {\n  args: {\n    id: 'default-switch',\n  },\n};\n\n/**\n * Use the `disabled` prop to disable the switch.\n */\nexport const Disabled: Story = {\n  args: {\n    id: 'disabled-switch',\n    disabled: true,\n  },\n};\n"], "names": [], "sourceRoot": ""}