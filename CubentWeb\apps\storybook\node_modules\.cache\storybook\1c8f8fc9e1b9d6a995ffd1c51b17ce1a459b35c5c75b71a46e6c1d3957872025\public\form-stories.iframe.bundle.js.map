{"version": 3, "file": "form-stories.iframe.bundle.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAEA;AAEA;AACA;AAUA;AACA;AAEA;AASA;AAIA;AAMA;AACA;AAAA;AAAA;AACA;AAAA;;;;;;;;;;;AAGA;AAXA;AAaA;;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AArBA;;;AAIA;;;AAuBA;AAIA;;AACA;AAEA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;;;;;;;;;;;AAIA;;AAZA;AAcA;;AAIA;AAEA;AAEA;AACA;AACA;AACA;AACA;;;;;;AAGA;;;AAXA;;;AAJA;AAiBA;;AACA;AAEA;AAEA;AACA;AACA;AAKA;AACA;;;;;;AAGA;;;;;;AAhBA;AAkBA;;AACA;AAEA;AAEA;AACA;AACA;AACA;;;;;;AAGA;;;AAVA;;;AADA;AAaA;;AACA;;AACA;AAEA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AAEA;;;;;;AAGA;;;AAjBA;;;AADA;AAoBA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7JA;;AAEA;AACA;AAEA;AAEA;AAIA;AAEA;AACA;AAIA;;;;;;AAGA;AAdA;AAgBA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvBA;AACA;AAEA;AACA;AAEA;AAUA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AAAA;;;;;;;;;;;;;AACA;AAEA;AAIA;AACA;AACA;AACA;AACA;AAEA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAEA;AAAA;;;;;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AAGA;AAAA;;;;;AAGA;;;;;;;;;;;;;;AAIA;AACA;AACA;AACA;;;;;;;;;;;;;;;;AAMA;AA1CA;AACA;;AADA;AA4CA;;;AAGA", "sources": ["webpack://storybook/../../packages/design-system/components/ui/form.tsx", "webpack://storybook/../../packages/design-system/components/ui/label.tsx", "webpack://storybook/./stories/form.stories.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport {\n  Controller,\n  FormProvider,\n  useFormContext,\n  useFormState,\n  type ControllerProps,\n  type FieldPath,\n  type FieldValues,\n} from \"react-hook-form\"\n\nimport { cn } from \"@repo/design-system/lib/utils\"\nimport { Label } from \"@repo/design-system/components/ui/label\"\n\nconst Form = FormProvider\n\ntype FormFieldContextValue<\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n> = {\n  name: TName\n}\n\nconst FormFieldContext = React.createContext<FormFieldContextValue>(\n  {} as FormFieldContextValue\n)\n\nconst FormField = <\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n>({\n  ...props\n}: ControllerProps<TFieldValues, TName>) => {\n  return (\n    <FormFieldContext.Provider value={{ name: props.name }}>\n      <Controller {...props} />\n    </FormFieldContext.Provider>\n  )\n}\n\nconst useFormField = () => {\n  const fieldContext = React.useContext(FormFieldContext)\n  const itemContext = React.useContext(FormItemContext)\n  const { getFieldState } = useFormContext()\n  const formState = useFormState({ name: fieldContext.name })\n  const fieldState = getFieldState(fieldContext.name, formState)\n\n  if (!fieldContext) {\n    throw new Error(\"useFormField should be used within <FormField>\")\n  }\n\n  const { id } = itemContext\n\n  return {\n    id,\n    name: fieldContext.name,\n    formItemId: `${id}-form-item`,\n    formDescriptionId: `${id}-form-item-description`,\n    formMessageId: `${id}-form-item-message`,\n    ...fieldState,\n  }\n}\n\ntype FormItemContextValue = {\n  id: string\n}\n\nconst FormItemContext = React.createContext<FormItemContextValue>(\n  {} as FormItemContextValue\n)\n\nfunction FormItem({ className, ...props }: React.ComponentProps<\"div\">) {\n  const id = React.useId()\n\n  return (\n    <FormItemContext.Provider value={{ id }}>\n      <div\n        data-slot=\"form-item\"\n        className={cn(\"grid gap-2\", className)}\n        {...props}\n      />\n    </FormItemContext.Provider>\n  )\n}\n\nfunction FormLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  const { error, formItemId } = useFormField()\n\n  return (\n    <Label\n      data-slot=\"form-label\"\n      data-error={!!error}\n      className={cn(\"data-[error=true]:text-destructive\", className)}\n      htmlFor={formItemId}\n      {...props}\n    />\n  )\n}\n\nfunction FormControl({ ...props }: React.ComponentProps<typeof Slot>) {\n  const { error, formItemId, formDescriptionId, formMessageId } = useFormField()\n\n  return (\n    <Slot\n      data-slot=\"form-control\"\n      id={formItemId}\n      aria-describedby={\n        !error\n          ? `${formDescriptionId}`\n          : `${formDescriptionId} ${formMessageId}`\n      }\n      aria-invalid={!!error}\n      {...props}\n    />\n  )\n}\n\nfunction FormDescription({ className, ...props }: React.ComponentProps<\"p\">) {\n  const { formDescriptionId } = useFormField()\n\n  return (\n    <p\n      data-slot=\"form-description\"\n      id={formDescriptionId}\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction FormMessage({ className, ...props }: React.ComponentProps<\"p\">) {\n  const { error, formMessageId } = useFormField()\n  const body = error ? String(error?.message ?? \"\") : props.children\n\n  if (!body) {\n    return null\n  }\n\n  return (\n    <p\n      data-slot=\"form-message\"\n      id={formMessageId}\n      className={cn(\"text-destructive text-sm\", className)}\n      {...props}\n    >\n      {body}\n    </p>\n  )\n}\n\nexport {\n  useFormField,\n  Form,\n  FormItem,\n  FormLabel,\n  FormControl,\n  FormDescription,\n  FormMessage,\n  FormField,\n}\n", "\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@repo/design-system/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n", "import { zodResolver } from '@hookform/resolvers/zod';\nimport { action } from '@storybook/addon-actions';\nimport type { Meta, StoryObj } from '@storybook/react';\nimport { useForm } from 'react-hook-form';\nimport * as z from 'zod';\n\nimport {\n  Form,\n  FormControl,\n  FormDescription,\n  FormField,\n  FormItem,\n  FormLabel,\n  FormMessage,\n} from '@repo/design-system/components/ui/form';\n\n/**\n * Building forms with React Hook Form and Zod.\n */\nconst meta: Meta<typeof Form> = {\n  title: 'ui/Form',\n  component: Form,\n  tags: ['autodocs'],\n  argTypes: {},\n  render: (args) => <ProfileForm {...args} />,\n} satisfies Meta<typeof Form>;\n\nexport default meta;\n\ntype Story = StoryObj<typeof meta>;\n\nconst formSchema = z.object({\n  username: z.string().min(2, {\n    message: 'Username must be at least 2 characters.',\n  }),\n});\n\nconst ProfileForm = (args: Story['args']) => {\n  const form = useForm<z.infer<typeof formSchema>>({\n    resolver: zod<PERSON><PERSON>olver(formSchema),\n    defaultValues: {\n      username: '',\n    },\n  });\n  function onSubmit(values: z.infer<typeof formSchema>) {\n    action('onSubmit')(values);\n  }\n  return (\n    <Form {...args} {...form}>\n      <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-8\">\n        <FormField\n          control={form.control}\n          name=\"username\"\n          render={({ field }) => (\n            <FormItem>\n              <FormLabel>Username</FormLabel>\n              <FormControl>\n                <input\n                  className=\"w-full rounded-md border border-input bg-background px-3 py-2\"\n                  placeholder=\"username\"\n                  {...field}\n                />\n              </FormControl>\n              <FormDescription>\n                This is your public display name.\n              </FormDescription>\n              <FormMessage />\n            </FormItem>\n          )}\n        />\n        <button\n          className=\"rounded bg-primary px-4 py-2 text-primary-foreground\"\n          type=\"submit\"\n        >\n          Submit\n        </button>\n      </form>\n    </Form>\n  );\n};\n\n/**\n * The default form of the form.\n */\nexport const Default: Story = {};\n"], "names": [], "sourceRoot": ""}