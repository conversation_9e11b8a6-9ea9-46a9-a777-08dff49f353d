#!/usr/bin/env pwsh
$basedir=Split-Path $MyInvocation.MyCommand.Definition -Parent

$exe=""
$pathsep=":"
$env_node_path=$env:NODE_PATH
$new_node_path="C:\Users\<USER>\Documents\2 FOLDERS FOR CUBENT\CubentWeb\node_modules\.pnpm\react-email@4.0.15_@opentel_35edadac6983ac8ade941caa1907d5c3\node_modules\react-email\dist\cli\node_modules;C:\Users\<USER>\Documents\2 FOLDERS FOR CUBENT\CubentWeb\node_modules\.pnpm\react-email@4.0.15_@opentel_35edadac6983ac8ade941caa1907d5c3\node_modules\react-email\dist\node_modules;C:\Users\<USER>\Documents\2 FOLDERS FOR CUBENT\CubentWeb\node_modules\.pnpm\react-email@4.0.15_@opentel_35edadac6983ac8ade941caa1907d5c3\node_modules\react-email\node_modules;C:\Users\<USER>\Documents\2 FOLDERS FOR CUBENT\CubentWeb\node_modules\.pnpm\react-email@4.0.15_@opentel_35edadac6983ac8ade941caa1907d5c3\node_modules;C:\Users\<USER>\Documents\2 FOLDERS FOR CUBENT\CubentWeb\node_modules\.pnpm\node_modules"
if ($PSVersionTable.PSVersion -lt "6.0" -or $IsWindows) {
  # Fix case when both the Windows and Linux builds of Node
  # are installed in the same directory
  $exe=".exe"
  $pathsep=";"
} else {
  $new_node_path="/proc/cygdrive/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/CubentWeb/node_modules/.pnpm/react-email@4.0.15_@opentel_35edadac6983ac8ade941caa1907d5c3/node_modules/react-email/dist/cli/node_modules:/proc/cygdrive/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/CubentWeb/node_modules/.pnpm/react-email@4.0.15_@opentel_35edadac6983ac8ade941caa1907d5c3/node_modules/react-email/dist/node_modules:/proc/cygdrive/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/CubentWeb/node_modules/.pnpm/react-email@4.0.15_@opentel_35edadac6983ac8ade941caa1907d5c3/node_modules/react-email/node_modules:/proc/cygdrive/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/CubentWeb/node_modules/.pnpm/react-email@4.0.15_@opentel_35edadac6983ac8ade941caa1907d5c3/node_modules:/proc/cygdrive/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/CubentWeb/node_modules/.pnpm/node_modules"
}
if ([string]::IsNullOrEmpty($env_node_path)) {
  $env:NODE_PATH=$new_node_path
} else {
  $env:NODE_PATH="$new_node_path$pathsep$env_node_path"
}

$ret=0
if (Test-Path "$basedir/node$exe") {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "$basedir/node$exe"  "$basedir/../react-email/dist/cli/index.mjs" $args
  } else {
    & "$basedir/node$exe"  "$basedir/../react-email/dist/cli/index.mjs" $args
  }
  $ret=$LASTEXITCODE
} else {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "node$exe"  "$basedir/../react-email/dist/cli/index.mjs" $args
  } else {
    & "node$exe"  "$basedir/../react-email/dist/cli/index.mjs" $args
  }
  $ret=$LASTEXITCODE
}
$env:NODE_PATH=$env_node_path
exit $ret
