{"version": 3, "file": "accordion-stories.iframe.bundle.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AAEA;AACA;AACA;AAEA;AAEA;AAGA;AAAA;AAAA;;;;;;AACA;AAJA;AAMA;AAIA;AAEA;AACA;AACA;;;;;;AAGA;AAXA;AAaA;AAKA;AACA;AACA;AACA;AACA;AAIA;;AAEA;AACA;AAAA;;;;;;;;;;;;;;;;;AAIA;AApBA;AAsBA;AAKA;AAEA;AACA;AACA;AAEA;AAAA;AAAA;;;;;;;;;;;AAGA;AAdA;AAgBA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/DA;AAOA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;;;;;AACA;AAAA;;;;;;;;;;AAIA;AAAA;AACA;AAAA;;;;;AACA;AAAA;;;;;;;;;;AAKA;AAAA;AACA;AAAA;;;;;AACA;AAAA;;;;;;;;;;;;;;;;;;;;;;;AAMA;AAEA;AAIA;;;AAGA", "sources": ["webpack://storybook/../../packages/design-system/components/ui/accordion.tsx", "webpack://storybook/./stories/accordion.stories.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AccordionPrimitive from \"@radix-ui/react-accordion\"\nimport { ChevronDownIcon } from \"lucide-react\"\n\nimport { cn } from \"@repo/design-system/lib/utils\"\n\nfunction Accordion({\n  ...props\n}: React.ComponentProps<typeof AccordionPrimitive.Root>) {\n  return <AccordionPrimitive.Root data-slot=\"accordion\" {...props} />\n}\n\nfunction AccordionItem({\n  className,\n  ...props\n}: React.ComponentProps<typeof AccordionPrimitive.Item>) {\n  return (\n    <AccordionPrimitive.Item\n      data-slot=\"accordion-item\"\n      className={cn(\"border-b last:border-b-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AccordionTrigger({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof AccordionPrimitive.Trigger>) {\n  return (\n    <AccordionPrimitive.Header className=\"flex\">\n      <AccordionPrimitive.Trigger\n        data-slot=\"accordion-trigger\"\n        className={cn(\n          \"focus-visible:border-ring focus-visible:ring-ring/50 flex flex-1 items-start justify-between gap-4 rounded-md py-4 text-left text-sm font-medium transition-all outline-none hover:underline focus-visible:ring-[3px] disabled:pointer-events-none disabled:opacity-50 [&[data-state=open]>svg]:rotate-180\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <ChevronDownIcon className=\"text-muted-foreground pointer-events-none size-4 shrink-0 translate-y-0.5 transition-transform duration-200\" />\n      </AccordionPrimitive.Trigger>\n    </AccordionPrimitive.Header>\n  )\n}\n\nfunction AccordionContent({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof AccordionPrimitive.Content>) {\n  return (\n    <AccordionPrimitive.Content\n      data-slot=\"accordion-content\"\n      className=\"data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down overflow-hidden text-sm\"\n      {...props}\n    >\n      <div className={cn(\"pt-0 pb-4\", className)}>{children}</div>\n    </AccordionPrimitive.Content>\n  )\n}\n\nexport { Accordion, AccordionItem, AccordionTrigger, AccordionContent }\n", "import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';\n\nimport {\n  Accordion,\n  AccordionContent,\n  AccordionItem,\n  AccordionTrigger,\n} from '@repo/design-system/components/ui/accordion';\n\n/**\n * A vertically stacked set of interactive headings that each reveal a section\n * of content.\n */\nconst meta = {\n  title: 'ui/Accordion',\n  component: Accordion,\n  tags: ['autodocs'],\n  argTypes: {\n    type: {\n      options: ['single', 'multiple'],\n      control: { type: 'radio' },\n    },\n  },\n  args: {\n    type: 'single',\n    collapsible: true,\n  },\n  render: (args) => (\n    <Accordion {...args}>\n      <AccordionItem value=\"item-1\">\n        <AccordionTrigger>Is it accessible?</AccordionTrigger>\n        <AccordionContent>\n          Yes. It adheres to the WAI-ARIA design pattern.\n        </AccordionContent>\n      </AccordionItem>\n      <AccordionItem value=\"item-2\">\n        <AccordionTrigger>Is it styled?</AccordionTrigger>\n        <AccordionContent>\n          Yes. It comes with default styles that matches the other components'\n          aesthetic.\n        </AccordionContent>\n      </AccordionItem>\n      <AccordionItem value=\"item-3\">\n        <AccordionTrigger>Is it animated?</AccordionTrigger>\n        <AccordionContent>\n          Yes. It's animated by default, but you can disable it if you prefer.\n        </AccordionContent>\n      </AccordionItem>\n    </Accordion>\n  ),\n} satisfies Meta<typeof Accordion>;\n\nexport default meta;\n\ntype Story = StoryObj<typeof meta>;\n\n/**\n * The default behavior of the accordion allows only one item to be open.\n */\nexport const Default: Story = {};\n"], "names": [], "sourceRoot": ""}