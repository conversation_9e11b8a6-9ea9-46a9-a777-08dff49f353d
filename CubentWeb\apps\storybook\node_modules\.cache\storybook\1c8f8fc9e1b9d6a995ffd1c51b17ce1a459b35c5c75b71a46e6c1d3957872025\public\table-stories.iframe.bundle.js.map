{"version": 3, "file": "table-stories.iframe.bundle.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AAEA;AAEA;AAEA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;AAIA;AAbA;AAeA;AACA;AAEA;AACA;AACA;;;;;;AAGA;AARA;AAUA;AACA;AAEA;AACA;AACA;;;;;;AAGA;AARA;AAUA;AACA;AAEA;AACA;AAIA;;;;;;AAGA;AAXA;AAaA;AACA;AAEA;AACA;AAIA;;;;;;AAGA;AAXA;AAaA;AACA;AAEA;AACA;AAIA;;;;;;AAGA;AAXA;AAaA;AACA;AAEA;AACA;AAIA;;;;;;AAGA;AAXA;AAaA;AAIA;AAEA;AACA;AACA;;;;;;AAGA;AAXA;AAaA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxGA;AAUA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAGA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;;;;;AACA;AACA;AACA;AAAA;AAAA;;;;;AACA;AAAA;;;;;AACA;AAAA;;;;;AACA;AAAA;AAAA;;;;;;;;;;;;;;;AAGA;AACA;AAEA;AAAA;AAAA;;;;;AACA;AAAA;;;;;AACA;AAAA;;;;;AACA;AAAA;AAAA;;;;;;AAJA;;;;;;;;;;;;;;;;;;;;;;AAUA;AAEA;AAIA;;;AAGA", "sources": ["webpack://storybook/../../packages/design-system/components/ui/table.tsx", "webpack://storybook/./stories/table.stories.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\n\nimport { cn } from \"@repo/design-system/lib/utils\"\n\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\n  return (\n    <div\n      data-slot=\"table-container\"\n      className=\"relative w-full overflow-x-auto\"\n    >\n      <table\n        data-slot=\"table\"\n        className={cn(\"w-full caption-bottom text-sm\", className)}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\n  return (\n    <thead\n      data-slot=\"table-header\"\n      className={cn(\"[&_tr]:border-b\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\n  return (\n    <tbody\n      data-slot=\"table-body\"\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\n  return (\n    <tfoot\n      data-slot=\"table-footer\"\n      className={cn(\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\n  return (\n    <tr\n      data-slot=\"table-row\"\n      className={cn(\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\n  return (\n    <th\n      data-slot=\"table-head\"\n      className={cn(\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\n  return (\n    <td\n      data-slot=\"table-cell\"\n      className={cn(\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCaption({\n  className,\n  ...props\n}: React.ComponentProps<\"caption\">) {\n  return (\n    <caption\n      data-slot=\"table-caption\"\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n", "import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';\n\nimport {\n  Table,\n  TableBody,\n  TableCaption,\n  TableCell,\n  TableHead,\n  TableHeader,\n  TableRow,\n} from '@repo/design-system/components/ui/table';\n\nconst invoices = [\n  {\n    invoice: 'INV001',\n    paymentStatus: 'Paid',\n    totalAmount: '$250.00',\n    paymentMethod: 'Credit Card',\n  },\n  {\n    invoice: 'INV002',\n    paymentStatus: 'Pending',\n    totalAmount: '$150.00',\n    paymentMethod: 'PayPal',\n  },\n  {\n    invoice: 'INV003',\n    paymentStatus: 'Unpaid',\n    totalAmount: '$350.00',\n    paymentMethod: 'Bank Transfer',\n  },\n  {\n    invoice: 'INV004',\n    paymentStatus: 'Paid',\n    totalAmount: '$450.00',\n    paymentMethod: 'Credit Card',\n  },\n];\n\n/**\n * Powerful table and datagrids built using TanStack Table.\n */\nconst meta = {\n  title: 'ui/Table',\n  component: Table,\n  tags: ['autodocs'],\n  argTypes: {},\n  render: (args) => (\n    <Table {...args}>\n      <TableCaption>A list of your recent invoices.</TableCaption>\n      <TableHeader>\n        <TableRow>\n          <TableHead className=\"w-[100px]\">Invoice</TableHead>\n          <TableHead>Status</TableHead>\n          <TableHead>Method</TableHead>\n          <TableHead className=\"text-right\">Amount</TableHead>\n        </TableRow>\n      </TableHeader>\n      <TableBody>\n        {invoices.map((invoice) => (\n          <TableRow key={invoice.invoice}>\n            <TableCell className=\"font-medium\">{invoice.invoice}</TableCell>\n            <TableCell>{invoice.paymentStatus}</TableCell>\n            <TableCell>{invoice.paymentMethod}</TableCell>\n            <TableCell className=\"text-right\">{invoice.totalAmount}</TableCell>\n          </TableRow>\n        ))}\n      </TableBody>\n    </Table>\n  ),\n} satisfies Meta<typeof Table>;\n\nexport default meta;\n\ntype Story = StoryObj<typeof meta>;\n\n/**\n * The default form of the table.\n */\nexport const Default: Story = {};\n"], "names": [], "sourceRoot": ""}