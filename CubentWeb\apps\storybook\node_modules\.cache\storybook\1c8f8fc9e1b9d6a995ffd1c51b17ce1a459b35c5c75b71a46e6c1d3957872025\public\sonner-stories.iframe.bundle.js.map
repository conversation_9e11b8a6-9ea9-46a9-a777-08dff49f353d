{"version": 3, "file": "sonner-stories.iframe.bundle.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACPA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;ACPA;AAEA;AAEA;AAEA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AAIA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;;;;;AAGA;AAAA;;;;;;;;;;;AAGA", "sources": ["webpack://storybook/../../node_modules/.pnpm/@storybook+addon-actions@8._fdbb51b12566af5e3312bafa313d86f1/node_modules/@storybook/addon-actions/dist/chunk-4XZ63LWV.mjs", "webpack://storybook/../../node_modules/.pnpm/@storybook+addon-actions@8._fdbb51b12566af5e3312bafa313d86f1/node_modules/@storybook/addon-actions/dist/index.mjs", "webpack://storybook/./stories/sonner.stories.tsx"], "sourcesContent": ["import { addons } from 'storybook/internal/preview-api';\nimport { ImplicitActionsDuringRendering } from 'storybook/internal/preview-errors';\nimport { global } from '@storybook/global';\nimport { v4 } from 'uuid';\n\nvar __defProp=Object.defineProperty;var __export=(target,all)=>{for(var name in all)__defProp(target,name,{get:all[name],enumerable:!0});};var PARAM_KEY=\"actions\",ADDON_ID=\"storybook/actions\",PANEL_ID=`${ADDON_ID}/panel`,EVENT_ID=`${ADDON_ID}/action-event`,CLEAR_ID=`${ADDON_ID}/action-clear`,CYCLIC_KEY=\"$___storybook.isCyclic\";var config={depth:10,clearOnStoryChange:!0,limit:50},configureActions=(options={})=>{Object.assign(config,options);};var findProto=(obj,callback)=>{let proto=Object.getPrototypeOf(obj);return !proto||callback(proto)?proto:findProto(proto,callback)},isReactSyntheticEvent=e=>!!(typeof e==\"object\"&&e&&findProto(e,proto=>/^Synthetic(?:Base)?Event$/.test(proto.constructor.name))&&typeof e.persist==\"function\"),serializeArg=a=>{if(isReactSyntheticEvent(a)){let e=Object.create(a.constructor.prototype,Object.getOwnPropertyDescriptors(a));e.persist();let viewDescriptor=Object.getOwnPropertyDescriptor(e,\"view\"),view=viewDescriptor?.value;return typeof view==\"object\"&&view?.constructor.name===\"Window\"&&Object.defineProperty(e,\"view\",{...viewDescriptor,value:Object.create(view.constructor.prototype)}),e}return a},generateId=()=>typeof crypto==\"object\"&&typeof crypto.getRandomValues==\"function\"?v4():Date.now().toString(36)+Math.random().toString(36).substring(2);function action(name,options={}){let actionOptions={...config,...options},handler=function(...args){if(options.implicit){let storyRenderer=(\"__STORYBOOK_PREVIEW__\"in global?global.__STORYBOOK_PREVIEW__:void 0)?.storyRenders.find(render=>render.phase===\"playing\"||render.phase===\"rendering\");if(storyRenderer){let deprecated=!globalThis?.FEATURES?.disallowImplicitActionsInRenderV8,error=new ImplicitActionsDuringRendering({phase:storyRenderer.phase,name,deprecated});if(deprecated)console.warn(error);else throw error}}let channel=addons.getChannel(),id=generateId(),minDepth=5,serializedArgs=args.map(serializeArg),normalizedArgs=args.length>1?serializedArgs:serializedArgs[0],actionDisplayToEmit={id,count:0,data:{name,args:normalizedArgs},options:{...actionOptions,maxDepth:minDepth+(actionOptions.depth||3),allowFunction:actionOptions.allowFunction||!1}};channel.emit(EVENT_ID,actionDisplayToEmit);};return handler.isAction=!0,handler.implicit=options.implicit,handler}var actions=(...args)=>{let options=config,names=args;names.length===1&&Array.isArray(names[0])&&([names]=names),names.length!==1&&typeof names[names.length-1]!=\"string\"&&(options={...config,...names.pop()});let namesObject=names[0];(names.length!==1||typeof namesObject==\"string\")&&(namesObject={},names.forEach(name=>{namesObject[name]=name;}));let actionsObject={};return Object.keys(namesObject).forEach(name=>{actionsObject[name]=action(namesObject[name],options);}),actionsObject};\n\nexport { ADDON_ID, CLEAR_ID, CYCLIC_KEY, EVENT_ID, PANEL_ID, PARAM_KEY, __export, action, actions, config, configureActions };\n", "import { __export, action } from './chunk-4XZ63LWV.mjs';\nexport { ADDON_ID, CLEAR_ID, CYCLIC_KEY, EVENT_ID, PANEL_ID, PARAM_KEY, action, actions, config, configureActions } from './chunk-4XZ63LWV.mjs';\nimport { definePreview } from 'storybook/internal/preview-api';\nimport { global } from '@storybook/global';\n\nvar preview_exports={};__export(preview_exports,{argsEnhancers:()=>argsEnhancers,loaders:()=>loaders});var isInInitialArgs=(name,initialArgs)=>typeof initialArgs[name]>\"u\"&&!(name in initialArgs),inferActionsFromArgTypesRegex=context=>{let{initialArgs,argTypes,id,parameters:{actions:actions2}}=context;if(!actions2||actions2.disable||!actions2.argTypesRegex||!argTypes)return {};let argTypesRegex=new RegExp(actions2.argTypesRegex);return Object.entries(argTypes).filter(([name])=>!!argTypesRegex.test(name)).reduce((acc,[name,argType])=>(isInInitialArgs(name,initialArgs)&&(acc[name]=action(name,{implicit:!0,id})),acc),{})},addActionsFromArgTypes=context=>{let{initialArgs,argTypes,parameters:{actions:actions2}}=context;return actions2?.disable||!argTypes?{}:Object.entries(argTypes).filter(([name,argType])=>!!argType.action).reduce((acc,[name,argType])=>(isInInitialArgs(name,initialArgs)&&(acc[name]=action(typeof argType.action==\"string\"?argType.action:name)),acc),{})};var argsEnhancers=[addActionsFromArgTypes,inferActionsFromArgTypesRegex];var subscribed=!1,logActionsWhenMockCalled=context=>{let{parameters:{actions:actions2}}=context;if(!actions2?.disable&&!subscribed&&\"__STORYBOOK_TEST_ON_MOCK_CALL__\"in global&&typeof global.__STORYBOOK_TEST_ON_MOCK_CALL__==\"function\"){let onMockCall=global.__STORYBOOK_TEST_ON_MOCK_CALL__;onMockCall((mock,args)=>{let name=mock.getMockName();name!==\"spy\"&&(!/^next\\/.*::/.test(name)||[\"next/router::useRouter()\",\"next/navigation::useRouter()\",\"next/navigation::redirect\",\"next/cache::\",\"next/headers::cookies().set\",\"next/headers::cookies().delete\",\"next/headers::headers().set\",\"next/headers::headers().delete\"].some(prefix=>name.startsWith(prefix)))&&action(name)(args);}),subscribed=!0;}},loaders=[logActionsWhenMockCalled];var index_default=()=>definePreview(preview_exports);\n\nexport { index_default as default };\n", "import { action } from '@storybook/addon-actions';\nimport type { Meta, StoryObj } from '@storybook/react';\nimport { toast } from 'sonner';\n\nimport { Toaster } from '@repo/design-system/components/ui/sonner';\n\n/**\n * An opinionated toast component for React.\n */\nconst meta: Meta<typeof Toaster> = {\n  title: 'ui/Sonner',\n  component: Toaster,\n  tags: ['autodocs'],\n  argTypes: {},\n  args: {\n    position: 'bottom-right',\n  },\n  parameters: {\n    layout: 'fullscreen',\n  },\n} satisfies Meta<typeof Toaster>;\n\nexport default meta;\n\ntype Story = StoryObj<typeof meta>;\n\n/**\n * The default form of the toaster.\n */\nexport const Default: Story = {\n  render: (args) => (\n    <div className=\"flex min-h-96 items-center justify-center space-x-2\">\n      <button\n        type=\"button\"\n        onClick={() =>\n          toast('Event has been created', {\n            description: new Date().toLocaleString(),\n            action: {\n              label: 'Undo',\n              onClick: action('Undo clicked'),\n            },\n          })\n        }\n      >\n        Show Toast\n      </button>\n      <Toaster {...args} />\n    </div>\n  ),\n};\n"], "names": [], "sourceRoot": ""}