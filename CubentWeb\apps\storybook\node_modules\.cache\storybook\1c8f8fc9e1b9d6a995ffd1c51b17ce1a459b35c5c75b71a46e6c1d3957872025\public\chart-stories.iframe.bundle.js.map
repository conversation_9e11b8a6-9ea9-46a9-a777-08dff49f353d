{"version": 3, "file": "chart-stories.iframe.bundle.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAEA;AACA;AAEA;AAEA;AACA;AAAA;AAAA;AAAA;AAgBA;AAEA;;AACA;AAEA;AACA;AACA;AAEA;AACA;AARA;AAUA;;AAYA;AACA;AAEA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAIA;;AAEA;AAAA;AAAA;;;;;;AACA;AACA;;;;;;;;;;;;;;;;;AAKA;;AAjCA;AAmCA;AACA;AAIA;AACA;AACA;AAEA;AAEA;AACA;AAGA;AACA;;AAEA;AAGA;AACA;;AAGA;AAGA;;;;;;AAGA;AA/BA;AAiCA;AAEA;;AAsBA;AAEA;AAAA;;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAKA;AACA;AACA;AACA;;;;;;AAGA;AAEA;AACA;AACA;AAEA;AAAA;AAAA;;;;;;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AAEA;AAEA;;AAKA;AACA;AAAA;AACA;AACA;AACA;AACA;AAEA;AAGA;AAKA;;AAIA;;;;AAGA;AAEA;AAGA;AACA;AACA;AAEA;AACA;AAEA;AAEA;AACA;AACA;;;;;;AAKA;AACA;;AAKA;AAAA;;AACA;AACA;AAAA;AACA;;;;;;;;;;;;AAGA;AACA;AACA;;;;;;;;;;;;;;AAhDA;;;;;AAwDA;;;;;;;;;;;;AAIA;;;AAxHA;;;AAtBA;AAgJA;AAEA;;AAWA;AAEA;AACA;AACA;AAEA;AAEA;AAMA;AACA;AACA;AAEA;AAGA;;AAIA;;;;AAGA;AACA;AACA;AACA;AACA;;;;;;AAGA;;AAfA;;;;;AAkBA;;;;;;AAGA;;;AAzCA;;;AAXA;AAsDA;AACA;AAKA;AACA;AACA;AAEA;AAOA;AAEA;AAIA;AACA;AAKA;AAGA;AAEA;AAGA;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxVA;AACA;AAcA;AAOA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AAIA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;;;;;AACA;AACA;AACA;AACA;AACA;AACA;;;;;AAEA;AACA;AACA;AAAA;;;;;;;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;AAKA;AAEA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;;;;;AACA;AACA;AACA;AACA;AACA;AACA;;;;;AAEA;AACA;AACA;AAAA;;;;;;;;;;AAEA;AAAA;AAAA;AAAA;;;;;AACA;AAAA;AAAA;AAAA;;;;;;;;;;;;;;;;AAIA;AAEA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;;;;;AACA;AACA;AACA;AACA;AACA;AACA;;;;;AAEA;AACA;AACA;AAAA;;;;;;;;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;AAKA;AAEA;;;AAGA;AACA;AACA;AACA;AACA;;AACA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;;;;;;;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;;;;;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;;;AAKA;AACA;;;;;;;;;;;;;;;;;;;;;AAMA;AACA", "sources": ["webpack://storybook/../../packages/design-system/components/ui/chart.tsx", "webpack://storybook/./stories/chart.stories.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as RechartsPrimitive from \"recharts\"\n\nimport { cn } from \"@repo/design-system/lib/utils\"\n\n// Format: { THEME_NAME: CSS_SELECTOR }\nconst THEMES = { light: \"\", dark: \".dark\" } as const\n\nexport type ChartConfig = {\n  [k in string]: {\n    label?: React.ReactNode\n    icon?: React.ComponentType\n  } & (\n    | { color?: string; theme?: never }\n    | { color?: never; theme: Record<keyof typeof THEMES, string> }\n  )\n}\n\ntype ChartContextProps = {\n  config: ChartConfig\n}\n\nconst ChartContext = React.createContext<ChartContextProps | null>(null)\n\nfunction useChart() {\n  const context = React.useContext(ChartContext)\n\n  if (!context) {\n    throw new Error(\"useChart must be used within a <ChartContainer />\")\n  }\n\n  return context\n}\n\nfunction ChartContainer({\n  id,\n  className,\n  children,\n  config,\n  ...props\n}: React.ComponentProps<\"div\"> & {\n  config: ChartConfig\n  children: React.ComponentProps<\n    typeof RechartsPrimitive.ResponsiveContainer\n  >[\"children\"]\n}) {\n  const uniqueId = React.useId()\n  const chartId = `chart-${id || uniqueId.replace(/:/g, \"\")}`\n\n  return (\n    <ChartContext.Provider value={{ config }}>\n      <div\n        data-slot=\"chart\"\n        data-chart={chartId}\n        className={cn(\n          \"[&_.recharts-cartesian-axis-tick_text]:fill-muted-foreground [&_.recharts-cartesian-grid_line[stroke='#ccc']]:stroke-border/50 [&_.recharts-curve.recharts-tooltip-cursor]:stroke-border [&_.recharts-polar-grid_[stroke='#ccc']]:stroke-border [&_.recharts-radial-bar-background-sector]:fill-muted [&_.recharts-rectangle.recharts-tooltip-cursor]:fill-muted [&_.recharts-reference-line_[stroke='#ccc']]:stroke-border flex aspect-video justify-center text-xs [&_.recharts-dot[stroke='#fff']]:stroke-transparent [&_.recharts-layer]:outline-hidden [&_.recharts-sector]:outline-hidden [&_.recharts-sector[stroke='#fff']]:stroke-transparent [&_.recharts-surface]:outline-hidden\",\n          className\n        )}\n        {...props}\n      >\n        <ChartStyle id={chartId} config={config} />\n        <RechartsPrimitive.ResponsiveContainer>\n          {children}\n        </RechartsPrimitive.ResponsiveContainer>\n      </div>\n    </ChartContext.Provider>\n  )\n}\n\nconst ChartStyle = ({ id, config }: { id: string; config: ChartConfig }) => {\n  const colorConfig = Object.entries(config).filter(\n    ([, config]) => config.theme || config.color\n  )\n\n  if (!colorConfig.length) {\n    return null\n  }\n\n  return (\n    <style\n      dangerouslySetInnerHTML={{\n        __html: Object.entries(THEMES)\n          .map(\n            ([theme, prefix]) => `\n${prefix} [data-chart=${id}] {\n${colorConfig\n  .map(([key, itemConfig]) => {\n    const color =\n      itemConfig.theme?.[theme as keyof typeof itemConfig.theme] ||\n      itemConfig.color\n    return color ? `  --color-${key}: ${color};` : null\n  })\n  .join(\"\\n\")}\n}\n`\n          )\n          .join(\"\\n\"),\n      }}\n    />\n  )\n}\n\nconst ChartTooltip = RechartsPrimitive.Tooltip\n\nfunction ChartTooltipContent({\n  active,\n  payload,\n  className,\n  indicator = \"dot\",\n  hideLabel = false,\n  hideIndicator = false,\n  label,\n  labelFormatter,\n  labelClassName,\n  formatter,\n  color,\n  nameKey,\n  labelKey,\n}: React.ComponentProps<typeof RechartsPrimitive.Tooltip> &\n  React.ComponentProps<\"div\"> & {\n    hideLabel?: boolean\n    hideIndicator?: boolean\n    indicator?: \"line\" | \"dot\" | \"dashed\"\n    nameKey?: string\n    labelKey?: string\n  }) {\n  const { config } = useChart()\n\n  const tooltipLabel = React.useMemo(() => {\n    if (hideLabel || !payload?.length) {\n      return null\n    }\n\n    const [item] = payload\n    const key = `${labelKey || item?.dataKey || item?.name || \"value\"}`\n    const itemConfig = getPayloadConfigFromPayload(config, item, key)\n    const value =\n      !labelKey && typeof label === \"string\"\n        ? config[label as keyof typeof config]?.label || label\n        : itemConfig?.label\n\n    if (labelFormatter) {\n      return (\n        <div className={cn(\"font-medium\", labelClassName)}>\n          {labelFormatter(value, payload)}\n        </div>\n      )\n    }\n\n    if (!value) {\n      return null\n    }\n\n    return <div className={cn(\"font-medium\", labelClassName)}>{value}</div>\n  }, [\n    label,\n    labelFormatter,\n    payload,\n    hideLabel,\n    labelClassName,\n    config,\n    labelKey,\n  ])\n\n  if (!active || !payload?.length) {\n    return null\n  }\n\n  const nestLabel = payload.length === 1 && indicator !== \"dot\"\n\n  return (\n    <div\n      className={cn(\n        \"border-border/50 bg-background grid min-w-[8rem] items-start gap-1.5 rounded-lg border px-2.5 py-1.5 text-xs shadow-xl\",\n        className\n      )}\n    >\n      {!nestLabel ? tooltipLabel : null}\n      <div className=\"grid gap-1.5\">\n        {payload.map((item, index) => {\n          const key = `${nameKey || item.name || item.dataKey || \"value\"}`\n          const itemConfig = getPayloadConfigFromPayload(config, item, key)\n          const indicatorColor = color || item.payload.fill || item.color\n\n          return (\n            <div\n              key={item.dataKey}\n              className={cn(\n                \"[&>svg]:text-muted-foreground flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5\",\n                indicator === \"dot\" && \"items-center\"\n              )}\n            >\n              {formatter && item?.value !== undefined && item.name ? (\n                formatter(item.value, item.name, item, index, item.payload)\n              ) : (\n                <>\n                  {itemConfig?.icon ? (\n                    <itemConfig.icon />\n                  ) : (\n                    !hideIndicator && (\n                      <div\n                        className={cn(\n                          \"shrink-0 rounded-[2px] border-(--color-border) bg-(--color-bg)\",\n                          {\n                            \"h-2.5 w-2.5\": indicator === \"dot\",\n                            \"w-1\": indicator === \"line\",\n                            \"w-0 border-[1.5px] border-dashed bg-transparent\":\n                              indicator === \"dashed\",\n                            \"my-0.5\": nestLabel && indicator === \"dashed\",\n                          }\n                        )}\n                        style={\n                          {\n                            \"--color-bg\": indicatorColor,\n                            \"--color-border\": indicatorColor,\n                          } as React.CSSProperties\n                        }\n                      />\n                    )\n                  )}\n                  <div\n                    className={cn(\n                      \"flex flex-1 justify-between leading-none\",\n                      nestLabel ? \"items-end\" : \"items-center\"\n                    )}\n                  >\n                    <div className=\"grid gap-1.5\">\n                      {nestLabel ? tooltipLabel : null}\n                      <span className=\"text-muted-foreground\">\n                        {itemConfig?.label || item.name}\n                      </span>\n                    </div>\n                    {item.value && (\n                      <span className=\"text-foreground font-mono font-medium tabular-nums\">\n                        {item.value.toLocaleString()}\n                      </span>\n                    )}\n                  </div>\n                </>\n              )}\n            </div>\n          )\n        })}\n      </div>\n    </div>\n  )\n}\n\nconst ChartLegend = RechartsPrimitive.Legend\n\nfunction ChartLegendContent({\n  className,\n  hideIcon = false,\n  payload,\n  verticalAlign = \"bottom\",\n  nameKey,\n}: React.ComponentProps<\"div\"> &\n  Pick<RechartsPrimitive.LegendProps, \"payload\" | \"verticalAlign\"> & {\n    hideIcon?: boolean\n    nameKey?: string\n  }) {\n  const { config } = useChart()\n\n  if (!payload?.length) {\n    return null\n  }\n\n  return (\n    <div\n      className={cn(\n        \"flex items-center justify-center gap-4\",\n        verticalAlign === \"top\" ? \"pb-3\" : \"pt-3\",\n        className\n      )}\n    >\n      {payload.map((item) => {\n        const key = `${nameKey || item.dataKey || \"value\"}`\n        const itemConfig = getPayloadConfigFromPayload(config, item, key)\n\n        return (\n          <div\n            key={item.value}\n            className={cn(\n              \"[&>svg]:text-muted-foreground flex items-center gap-1.5 [&>svg]:h-3 [&>svg]:w-3\"\n            )}\n          >\n            {itemConfig?.icon && !hideIcon ? (\n              <itemConfig.icon />\n            ) : (\n              <div\n                className=\"h-2 w-2 shrink-0 rounded-[2px]\"\n                style={{\n                  backgroundColor: item.color,\n                }}\n              />\n            )}\n            {itemConfig?.label}\n          </div>\n        )\n      })}\n    </div>\n  )\n}\n\n// Helper to extract item config from a payload.\nfunction getPayloadConfigFromPayload(\n  config: ChartConfig,\n  payload: unknown,\n  key: string\n) {\n  if (typeof payload !== \"object\" || payload === null) {\n    return undefined\n  }\n\n  const payloadPayload =\n    \"payload\" in payload &&\n    typeof payload.payload === \"object\" &&\n    payload.payload !== null\n      ? payload.payload\n      : undefined\n\n  let configLabelKey: string = key\n\n  if (\n    key in payload &&\n    typeof payload[key as keyof typeof payload] === \"string\"\n  ) {\n    configLabelKey = payload[key as keyof typeof payload] as string\n  } else if (\n    payloadPayload &&\n    key in payloadPayload &&\n    typeof payloadPayload[key as keyof typeof payloadPayload] === \"string\"\n  ) {\n    configLabelKey = payloadPayload[\n      key as keyof typeof payloadPayload\n    ] as string\n  }\n\n  return configLabelKey in config\n    ? config[configLabelKey]\n    : config[key as keyof typeof config]\n}\n\nexport {\n  ChartContainer,\n  ChartTooltip,\n  ChartTooltipContent,\n  ChartLegend,\n  ChartLegendContent,\n  ChartStyle,\n}\n", "import type { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react';\nimport { useMemo } from 'react';\nimport {\n  Area,\n  AreaChart,\n  Bar,\n  BarChart,\n  CartesianGrid,\n  Label,\n  Line,\n  LineChart,\n  Pie,\n  <PERSON><PERSON>hart,\n  XAxis,\n} from 'recharts';\n\nimport {\n  type ChartConfig,\n  ChartContainer,\n  ChartTooltip,\n  ChartTooltipContent,\n} from '@repo/design-system/components/ui/chart';\n\nconst multiSeriesData = [\n  { month: 'January', desktop: 186, mobile: 80 },\n  { month: 'February', desktop: 305, mobile: 200 },\n  { month: 'March', desktop: 237, mobile: 120 },\n  { month: 'April', desktop: 73, mobile: 190 },\n  { month: 'May', desktop: 209, mobile: 130 },\n  { month: 'June', desktop: 214, mobile: 140 },\n];\n\nconst multiSeriesConfig = {\n  desktop: {\n    label: 'Desktop',\n    color: 'hsl(var(--chart-1))',\n  },\n  mobile: {\n    label: 'Mobile',\n    color: 'hsl(var(--chart-2))',\n  },\n} satisfies ChartConfig;\n\nconst singleSeriesData = [\n  { browser: 'chrome', visitors: 275, fill: 'var(--color-chrome)' },\n  { browser: 'safari', visitors: 200, fill: 'var(--color-safari)' },\n  { browser: 'other', visitors: 190, fill: 'var(--color-other)' },\n];\n\nconst singleSeriesConfig = {\n  visitors: {\n    label: 'Visitors',\n  },\n  chrome: {\n    label: 'Chrome',\n    color: 'hsl(var(--chart-1))',\n  },\n  safari: {\n    label: 'Safari',\n    color: 'hsl(var(--chart-2))',\n  },\n  other: {\n    label: 'Other',\n    color: 'hsl(var(--chart-5))',\n  },\n} satisfies ChartConfig;\n\n/**\n * Beautiful charts. Built using Recharts. Copy and paste into your apps.\n */\nconst meta = {\n  title: 'ui/Chart',\n  component: ChartContainer,\n  tags: ['autodocs'],\n  argTypes: {},\n  args: {\n    children: <div />,\n  },\n} satisfies Meta<typeof ChartContainer>;\n\nexport default meta;\n\ntype Story = StoryObj<typeof meta>;\n\n/**\n * Combine multiple Area components to create a stacked area chart.\n */\nexport const StackedAreaChart: Story = {\n  args: {\n    config: multiSeriesConfig,\n  },\n  render: (args) => (\n    <ChartContainer {...args}>\n      <AreaChart\n        accessibilityLayer\n        data={multiSeriesData}\n        margin={{\n          left: 12,\n          right: 12,\n        }}\n      >\n        <CartesianGrid vertical={false} />\n        <XAxis\n          dataKey=\"month\"\n          tickLine={false}\n          axisLine={false}\n          tickMargin={8}\n          tickFormatter={(value) => value.slice(0, 3)}\n        />\n        <ChartTooltip\n          cursor={false}\n          content={<ChartTooltipContent indicator=\"dot\" />}\n        />\n        <Area\n          dataKey=\"mobile\"\n          type=\"natural\"\n          fill=\"var(--color-mobile)\"\n          fillOpacity={0.4}\n          stroke=\"var(--color-mobile)\"\n          stackId=\"a\"\n        />\n        <Area\n          dataKey=\"desktop\"\n          type=\"natural\"\n          fill=\"var(--color-desktop)\"\n          fillOpacity={0.4}\n          stroke=\"var(--color-desktop)\"\n          stackId=\"a\"\n        />\n      </AreaChart>\n    </ChartContainer>\n  ),\n};\n\n/**\n * Combine multiple Bar components to create a stacked bar chart.\n */\nexport const StackedBarChart: Story = {\n  args: {\n    config: multiSeriesConfig,\n  },\n  render: (args) => (\n    <ChartContainer {...args}>\n      <BarChart accessibilityLayer data={multiSeriesData}>\n        <CartesianGrid vertical={false} />\n        <XAxis\n          dataKey=\"month\"\n          tickLine={false}\n          tickMargin={10}\n          axisLine={false}\n          tickFormatter={(value) => value.slice(0, 3)}\n        />\n        <ChartTooltip\n          cursor={false}\n          content={<ChartTooltipContent indicator=\"dashed\" />}\n        />\n        <Bar dataKey=\"desktop\" fill=\"var(--color-desktop)\" radius={4} />\n        <Bar dataKey=\"mobile\" fill=\"var(--color-mobile)\" radius={4} />\n      </BarChart>\n    </ChartContainer>\n  ),\n};\n\n/**\n * Combine multiple Line components to create a single line chart.\n */\nexport const MultiLineChart: Story = {\n  args: {\n    config: multiSeriesConfig,\n  },\n  render: (args) => (\n    <ChartContainer {...args}>\n      <LineChart\n        accessibilityLayer\n        data={multiSeriesData}\n        margin={{\n          left: 12,\n          right: 12,\n        }}\n      >\n        <CartesianGrid vertical={false} />\n        <XAxis\n          dataKey=\"month\"\n          tickLine={false}\n          axisLine={false}\n          tickMargin={8}\n          tickFormatter={(value) => value.slice(0, 3)}\n        />\n        <ChartTooltip\n          cursor={false}\n          content={<ChartTooltipContent hideLabel />}\n        />\n        <Line\n          dataKey=\"desktop\"\n          type=\"natural\"\n          stroke=\"var(--color-desktop)\"\n          strokeWidth={2}\n          dot={false}\n        />\n        <Line\n          dataKey=\"mobile\"\n          type=\"natural\"\n          stroke=\"var(--color-mobile)\"\n          strokeWidth={2}\n          dot={false}\n        />\n      </LineChart>\n    </ChartContainer>\n  ),\n};\n\n/**\n * Combine Pie and Label components to create a doughnut chart.\n */\nexport const DoughnutChart: Story = {\n  args: {\n    config: singleSeriesConfig,\n  },\n  render: (args) => {\n    const totalVisitors = useMemo(() => {\n      return singleSeriesData.reduce((acc, curr) => acc + curr.visitors, 0);\n    }, []);\n    return (\n      <ChartContainer {...args}>\n        <PieChart>\n          <ChartTooltip\n            cursor={false}\n            content={<ChartTooltipContent hideLabel />}\n          />\n          <Pie\n            data={singleSeriesData}\n            dataKey=\"visitors\"\n            nameKey=\"browser\"\n            innerRadius={48}\n            strokeWidth={5}\n          >\n            <Label\n              content={({ viewBox }) => {\n                if (viewBox && 'cx' in viewBox && 'cy' in viewBox) {\n                  return (\n                    <text\n                      x={viewBox.cx}\n                      y={viewBox.cy}\n                      textAnchor=\"middle\"\n                      dominantBaseline=\"middle\"\n                    >\n                      <tspan\n                        x={viewBox.cx}\n                        y={viewBox.cy}\n                        className=\"fill-foreground font-bold text-3xl\"\n                      >\n                        {totalVisitors.toLocaleString()}\n                      </tspan>\n                      <tspan\n                        x={viewBox.cx}\n                        y={(viewBox.cy || 0) + 24}\n                        className=\"fill-muted-foreground\"\n                      >\n                        Visitors\n                      </tspan>\n                    </text>\n                  );\n                }\n              }}\n            />\n          </Pie>\n        </PieChart>\n      </ChartContainer>\n    );\n  },\n};\n"], "names": [], "sourceRoot": ""}