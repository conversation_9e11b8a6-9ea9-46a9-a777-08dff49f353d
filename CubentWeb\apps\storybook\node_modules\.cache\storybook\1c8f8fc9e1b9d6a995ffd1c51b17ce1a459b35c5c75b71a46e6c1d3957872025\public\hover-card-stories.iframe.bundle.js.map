{"version": 3, "file": "hover-card-stories.iframe.bundle.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA;;AAEA;AACA;AAEA;AAEA;AAGA;AAAA;AAAA;;;;;;AACA;AAJA;AAMA;AAGA;AACA;AAAA;;;;;;AAEA;AANA;AAQA;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AAIA;;;;;;;;;;;AAIA;AApBA;AAsBA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzCA;AAMA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;;;;;AACA;AAAA;;;;;;;;;;;AAKA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AAIA;;;AAGA;AAEA;;;;AAIA;AACA;AACA;AACA;AACA;AACA", "sources": ["webpack://storybook/../../packages/design-system/components/ui/hover-card.tsx", "webpack://storybook/./stories/hover-card.stories.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as HoverCardPrimitive from \"@radix-ui/react-hover-card\"\n\nimport { cn } from \"@repo/design-system/lib/utils\"\n\nfunction HoverCard({\n  ...props\n}: React.ComponentProps<typeof HoverCardPrimitive.Root>) {\n  return <HoverCardPrimitive.Root data-slot=\"hover-card\" {...props} />\n}\n\nfunction HoverCardTrigger({\n  ...props\n}: React.ComponentProps<typeof HoverCardPrimitive.Trigger>) {\n  return (\n    <HoverCardPrimitive.Trigger data-slot=\"hover-card-trigger\" {...props} />\n  )\n}\n\nfunction HoverCardContent({\n  className,\n  align = \"center\",\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof HoverCardPrimitive.Content>) {\n  return (\n    <HoverCardPrimitive.Portal data-slot=\"hover-card-portal\">\n      <HoverCardPrimitive.Content\n        data-slot=\"hover-card-content\"\n        align={align}\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-64 origin-(--radix-hover-card-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden\",\n          className\n        )}\n        {...props}\n      />\n    </HoverCardPrimitive.Portal>\n  )\n}\n\nexport { HoverCard, HoverCardTrigger, HoverCardContent }\n", "import type { <PERSON>a, StoryObj } from '@storybook/react';\n\nimport {\n  HoverCard,\n  HoverCardContent,\n  HoverCardTrigger,\n} from '@repo/design-system/components/ui/hover-card';\n\n/**\n * For sighted users to preview content available behind a link.\n */\nconst meta = {\n  title: 'ui/HoverCard',\n  component: HoverCard,\n  tags: ['autodocs'],\n  argTypes: {},\n  args: {},\n  render: (args) => (\n    <HoverCard {...args}>\n      <HoverCardTrigger>Hover</HoverCardTrigger>\n      <HoverCardContent>\n        The React Framework - created and maintained by @vercel.\n      </HoverCardContent>\n    </HoverCard>\n  ),\n  parameters: {\n    layout: 'centered',\n  },\n} satisfies Meta<typeof HoverCard>;\n\nexport default meta;\n\ntype Story = StoryObj<typeof meta>;\n\n/**\n * The default form of the hover card.\n */\nexport const Default: Story = {};\n\n/**\n * Use the `openDelay` and `closeDelay` props to control the delay before the\n * hover card opens and closes.\n */\nexport const Instant: Story = {\n  args: {\n    openDelay: 0,\n    closeDelay: 0,\n  },\n};\n"], "names": [], "sourceRoot": ""}