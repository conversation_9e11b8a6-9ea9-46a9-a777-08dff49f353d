"use strict";
(self["webpackChunkstorybook"] = self["webpackChunkstorybook"] || []).push([["table-stories"],{

/***/ "../../packages/design-system/components/ui/table.tsx":
/*!************************************************************!*\
  !*** ../../packages/design-system/components/ui/table.tsx ***!
  \************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Table: () => (/* binding */ Table),
/* harmony export */   TableBody: () => (/* binding */ TableBody),
/* harmony export */   TableCaption: () => (/* binding */ TableCaption),
/* harmony export */   TableCell: () => (/* binding */ TableCell),
/* harmony export */   TableFooter: () => (/* binding */ TableFooter),
/* harmony export */   TableHead: () => (/* binding */ TableHead),
/* harmony export */   TableHeader: () => (/* binding */ TableHeader),
/* harmony export */   TableRow: () => (/* binding */ TableRow)
/* harmony export */ });
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/jsx-dev-runtime.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @repo/design-system/lib/utils */ "../../packages/design-system/lib/utils.ts");
/* provided dependency */ var __react_refresh_utils__ = __webpack_require__(/*! ../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js */ "../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js");
__webpack_require__.$Refresh$.runtime = __webpack_require__(/*! ../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js */ "../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js");

"use client";



function Table({ className, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
        "data-slot": "table-container",
        className: "relative w-full overflow-x-auto",
        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("table", {
            "data-slot": "table",
            className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)("w-full caption-bottom text-sm", className),
            ...props
        }, void 0, false, {
            fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\packages\\design-system\\components\\ui\\table.tsx",
            lineNumber: 13,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\packages\\design-system\\components\\ui\\table.tsx",
        lineNumber: 9,
        columnNumber: 5
    }, this);
}
_c = Table;
function TableHeader({ className, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("thead", {
        "data-slot": "table-header",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)("[&_tr]:border-b", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\packages\\design-system\\components\\ui\\table.tsx",
        lineNumber: 24,
        columnNumber: 5
    }, this);
}
_c1 = TableHeader;
function TableBody({ className, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("tbody", {
        "data-slot": "table-body",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)("[&_tr:last-child]:border-0", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\packages\\design-system\\components\\ui\\table.tsx",
        lineNumber: 34,
        columnNumber: 5
    }, this);
}
_c2 = TableBody;
function TableFooter({ className, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("tfoot", {
        "data-slot": "table-footer",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)("bg-muted/50 border-t font-medium [&>tr]:last:border-b-0", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\packages\\design-system\\components\\ui\\table.tsx",
        lineNumber: 44,
        columnNumber: 5
    }, this);
}
_c3 = TableFooter;
function TableRow({ className, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("tr", {
        "data-slot": "table-row",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\packages\\design-system\\components\\ui\\table.tsx",
        lineNumber: 57,
        columnNumber: 5
    }, this);
}
_c4 = TableRow;
function TableHead({ className, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("th", {
        "data-slot": "table-head",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\packages\\design-system\\components\\ui\\table.tsx",
        lineNumber: 70,
        columnNumber: 5
    }, this);
}
_c5 = TableHead;
function TableCell({ className, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("td", {
        "data-slot": "table-cell",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\packages\\design-system\\components\\ui\\table.tsx",
        lineNumber: 83,
        columnNumber: 5
    }, this);
}
_c6 = TableCell;
function TableCaption({ className, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("caption", {
        "data-slot": "table-caption",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)("text-muted-foreground mt-4 text-sm", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\packages\\design-system\\components\\ui\\table.tsx",
        lineNumber: 99,
        columnNumber: 5
    }, this);
}
_c7 = TableCaption;

Table.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "Table"
};
TableHeader.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "TableHeader"
};
TableBody.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "TableBody"
};
TableFooter.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "TableFooter"
};
TableHead.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "TableHead"
};
TableRow.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "TableRow"
};
TableCell.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "TableCell"
};
TableCaption.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "TableCaption"
};
var _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7;
__webpack_require__.$Refresh$.register(_c, "Table");
__webpack_require__.$Refresh$.register(_c1, "TableHeader");
__webpack_require__.$Refresh$.register(_c2, "TableBody");
__webpack_require__.$Refresh$.register(_c3, "TableFooter");
__webpack_require__.$Refresh$.register(_c4, "TableRow");
__webpack_require__.$Refresh$.register(_c5, "TableHead");
__webpack_require__.$Refresh$.register(_c6, "TableCell");
__webpack_require__.$Refresh$.register(_c7, "TableCaption");


const $ReactRefreshModuleId$ = __webpack_require__.$Refresh$.moduleId;
const $ReactRefreshCurrentExports$ = __react_refresh_utils__.getModuleExports(
	$ReactRefreshModuleId$
);

function $ReactRefreshModuleRuntime$(exports) {
	if (true) {
		let errorOverlay;
		if (true) {
			errorOverlay = false;
		}
		let testMode;
		if (typeof __react_refresh_test__ !== 'undefined') {
			testMode = __react_refresh_test__;
		}
		return __react_refresh_utils__.executeRuntime(
			exports,
			$ReactRefreshModuleId$,
			module.hot,
			errorOverlay,
			testMode
		);
	}
}

if (typeof Promise !== 'undefined' && $ReactRefreshCurrentExports$ instanceof Promise) {
	$ReactRefreshCurrentExports$.then($ReactRefreshModuleRuntime$);
} else {
	$ReactRefreshModuleRuntime$($ReactRefreshCurrentExports$);
}

/***/ }),

/***/ "./stories/table.stories.tsx":
/*!***********************************!*\
  !*** ./stories/table.stories.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Default: () => (/* binding */ Default),
/* harmony export */   __namedExportsOrder: () => (/* binding */ __namedExportsOrder),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/jsx-dev-runtime.js");
/* harmony import */ var _repo_design_system_components_ui_table__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @repo/design-system/components/ui/table */ "../../packages/design-system/components/ui/table.tsx");
/* provided dependency */ var __react_refresh_utils__ = __webpack_require__(/*! ../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js */ "../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js");
__webpack_require__.$Refresh$.runtime = __webpack_require__(/*! ../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js */ "../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js");



const invoices = [{
  invoice: 'INV001',
  paymentStatus: 'Paid',
  totalAmount: '$250.00',
  paymentMethod: 'Credit Card'
}, {
  invoice: 'INV002',
  paymentStatus: 'Pending',
  totalAmount: '$150.00',
  paymentMethod: 'PayPal'
}, {
  invoice: 'INV003',
  paymentStatus: 'Unpaid',
  totalAmount: '$350.00',
  paymentMethod: 'Bank Transfer'
}, {
  invoice: 'INV004',
  paymentStatus: 'Paid',
  totalAmount: '$450.00',
  paymentMethod: 'Credit Card'
}];
/**
 * Powerful table and datagrids built using TanStack Table.
 */
const meta = {
  title: 'ui/Table',
  component: _repo_design_system_components_ui_table__WEBPACK_IMPORTED_MODULE_1__.Table,
  tags: ['autodocs'],
  argTypes: {},
  render: args => /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_table__WEBPACK_IMPORTED_MODULE_1__.Table, {
    ...args,
    children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_table__WEBPACK_IMPORTED_MODULE_1__.TableCaption, {
      children: "A list of your recent invoices."
    }, void 0, false, {
      fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\table.stories.tsx",
      lineNumber: 50,
      columnNumber: 7
    }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_table__WEBPACK_IMPORTED_MODULE_1__.TableHeader, {
      children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_table__WEBPACK_IMPORTED_MODULE_1__.TableRow, {
        children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_table__WEBPACK_IMPORTED_MODULE_1__.TableHead, {
          className: "w-[100px]",
          children: "Invoice"
        }, void 0, false, {
          fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\table.stories.tsx",
          lineNumber: 53,
          columnNumber: 11
        }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_table__WEBPACK_IMPORTED_MODULE_1__.TableHead, {
          children: "Status"
        }, void 0, false, {
          fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\table.stories.tsx",
          lineNumber: 54,
          columnNumber: 11
        }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_table__WEBPACK_IMPORTED_MODULE_1__.TableHead, {
          children: "Method"
        }, void 0, false, {
          fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\table.stories.tsx",
          lineNumber: 55,
          columnNumber: 11
        }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_table__WEBPACK_IMPORTED_MODULE_1__.TableHead, {
          className: "text-right",
          children: "Amount"
        }, void 0, false, {
          fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\table.stories.tsx",
          lineNumber: 56,
          columnNumber: 11
        }, undefined)]
      }, void 0, true, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\table.stories.tsx",
        lineNumber: 52,
        columnNumber: 9
      }, undefined)
    }, void 0, false, {
      fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\table.stories.tsx",
      lineNumber: 51,
      columnNumber: 7
    }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_table__WEBPACK_IMPORTED_MODULE_1__.TableBody, {
      children: invoices.map(invoice => /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_table__WEBPACK_IMPORTED_MODULE_1__.TableRow, {
        children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_table__WEBPACK_IMPORTED_MODULE_1__.TableCell, {
          className: "font-medium",
          children: invoice.invoice
        }, void 0, false, {
          fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\table.stories.tsx",
          lineNumber: 62,
          columnNumber: 13
        }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_table__WEBPACK_IMPORTED_MODULE_1__.TableCell, {
          children: invoice.paymentStatus
        }, void 0, false, {
          fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\table.stories.tsx",
          lineNumber: 63,
          columnNumber: 13
        }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_table__WEBPACK_IMPORTED_MODULE_1__.TableCell, {
          children: invoice.paymentMethod
        }, void 0, false, {
          fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\table.stories.tsx",
          lineNumber: 64,
          columnNumber: 13
        }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_table__WEBPACK_IMPORTED_MODULE_1__.TableCell, {
          className: "text-right",
          children: invoice.totalAmount
        }, void 0, false, {
          fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\table.stories.tsx",
          lineNumber: 65,
          columnNumber: 13
        }, undefined)]
      }, invoice.invoice, true, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\table.stories.tsx",
        lineNumber: 61,
        columnNumber: 11
      }, undefined))
    }, void 0, false, {
      fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\table.stories.tsx",
      lineNumber: 59,
      columnNumber: 7
    }, undefined)]
  }, void 0, true, {
    fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\table.stories.tsx",
    lineNumber: 49,
    columnNumber: 5
  }, undefined),
  parameters: {
    docs: {
      description: {
        component: "Powerful table and datagrids built using TanStack Table."
      }
    }
  }
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (meta);
/**
 * The default form of the table.
 */
const Default = {};
;
const __namedExportsOrder = ["Default"];
Default.parameters = {
  ...Default.parameters,
  docs: {
    ...Default.parameters?.docs,
    source: {
      originalSource: "{}",
      ...Default.parameters?.docs?.source
    },
    description: {
      story: "The default form of the table.",
      ...Default.parameters?.docs?.description
    }
  }
};

const $ReactRefreshModuleId$ = __webpack_require__.$Refresh$.moduleId;
const $ReactRefreshCurrentExports$ = __react_refresh_utils__.getModuleExports(
	$ReactRefreshModuleId$
);

function $ReactRefreshModuleRuntime$(exports) {
	if (true) {
		let errorOverlay;
		if (true) {
			errorOverlay = false;
		}
		let testMode;
		if (typeof __react_refresh_test__ !== 'undefined') {
			testMode = __react_refresh_test__;
		}
		return __react_refresh_utils__.executeRuntime(
			exports,
			$ReactRefreshModuleId$,
			module.hot,
			errorOverlay,
			testMode
		);
	}
}

if (typeof Promise !== 'undefined' && $ReactRefreshCurrentExports$ instanceof Promise) {
	$ReactRefreshCurrentExports$.then($ReactRefreshModuleRuntime$);
} else {
	$ReactRefreshModuleRuntime$($ReactRefreshCurrentExports$);
}

/***/ })

}]);
//# sourceMappingURL=table-stories.iframe.bundle.js.map