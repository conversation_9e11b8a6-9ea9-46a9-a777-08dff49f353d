"use strict";
(self["webpackChunkstorybook"] = self["webpackChunkstorybook"] || []).push([["menubar-stories"],{

/***/ "../../packages/design-system/components/ui/menubar.tsx":
/*!**************************************************************!*\
  !*** ../../packages/design-system/components/ui/menubar.tsx ***!
  \**************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Menubar: () => (/* binding */ Menubar),
/* harmony export */   MenubarCheckboxItem: () => (/* binding */ MenubarCheckboxItem),
/* harmony export */   MenubarContent: () => (/* binding */ MenubarContent),
/* harmony export */   MenubarGroup: () => (/* binding */ MenubarGroup),
/* harmony export */   MenubarItem: () => (/* binding */ MenubarItem),
/* harmony export */   MenubarLabel: () => (/* binding */ MenubarLabel),
/* harmony export */   MenubarMenu: () => (/* binding */ MenubarMenu),
/* harmony export */   MenubarPortal: () => (/* binding */ MenubarPortal),
/* harmony export */   MenubarRadioGroup: () => (/* binding */ MenubarRadioGroup),
/* harmony export */   MenubarRadioItem: () => (/* binding */ MenubarRadioItem),
/* harmony export */   MenubarSeparator: () => (/* binding */ MenubarSeparator),
/* harmony export */   MenubarShortcut: () => (/* binding */ MenubarShortcut),
/* harmony export */   MenubarSub: () => (/* binding */ MenubarSub),
/* harmony export */   MenubarSubContent: () => (/* binding */ MenubarSubContent),
/* harmony export */   MenubarSubTrigger: () => (/* binding */ MenubarSubTrigger),
/* harmony export */   MenubarTrigger: () => (/* binding */ MenubarTrigger)
/* harmony export */ });
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/jsx-dev-runtime.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _radix_ui_react_menubar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-menubar */ "../../node_modules/.pnpm/@radix-ui+react-menubar@1.1_e943c26f42656451529ab5989f075360/node_modules/@radix-ui/react-menubar/dist/index.mjs");
/* harmony import */ var _barrel_optimize_names_CheckIcon_ChevronRightIcon_CircleIcon_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,ChevronRightIcon,CircleIcon!=!lucide-react */ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/check.js");
/* harmony import */ var _barrel_optimize_names_CheckIcon_ChevronRightIcon_CircleIcon_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,ChevronRightIcon,CircleIcon!=!lucide-react */ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/circle.js");
/* harmony import */ var _barrel_optimize_names_CheckIcon_ChevronRightIcon_CircleIcon_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,ChevronRightIcon,CircleIcon!=!lucide-react */ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/chevron-right.js");
/* harmony import */ var _repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @repo/design-system/lib/utils */ "../../packages/design-system/lib/utils.ts");
/* provided dependency */ var __react_refresh_utils__ = __webpack_require__(/*! ../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js */ "../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js");
__webpack_require__.$Refresh$.runtime = __webpack_require__(/*! ../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js */ "../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js");

"use client";





function Menubar({ className, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_menubar__WEBPACK_IMPORTED_MODULE_3__.Root, {
        "data-slot": "menubar",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)("bg-background flex h-9 items-center gap-1 rounded-md border p-1 shadow-xs", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\packages\\design-system\\components\\ui\\menubar.tsx",
        lineNumber: 14,
        columnNumber: 5
    }, this);
}
_c = Menubar;
function MenubarMenu({ ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_menubar__WEBPACK_IMPORTED_MODULE_3__.Menu, {
        "data-slot": "menubar-menu",
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\packages\\design-system\\components\\ui\\menubar.tsx",
        lineNumber: 28,
        columnNumber: 10
    }, this);
}
_c1 = MenubarMenu;
function MenubarGroup({ ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_menubar__WEBPACK_IMPORTED_MODULE_3__.Group, {
        "data-slot": "menubar-group",
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\packages\\design-system\\components\\ui\\menubar.tsx",
        lineNumber: 34,
        columnNumber: 10
    }, this);
}
_c2 = MenubarGroup;
function MenubarPortal({ ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_menubar__WEBPACK_IMPORTED_MODULE_3__.Portal, {
        "data-slot": "menubar-portal",
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\packages\\design-system\\components\\ui\\menubar.tsx",
        lineNumber: 40,
        columnNumber: 10
    }, this);
}
_c3 = MenubarPortal;
function MenubarRadioGroup({ ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_menubar__WEBPACK_IMPORTED_MODULE_3__.RadioGroup, {
        "data-slot": "menubar-radio-group",
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\packages\\design-system\\components\\ui\\menubar.tsx",
        lineNumber: 47,
        columnNumber: 5
    }, this);
}
_c4 = MenubarRadioGroup;
function MenubarTrigger({ className, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_menubar__WEBPACK_IMPORTED_MODULE_3__.Trigger, {
        "data-slot": "menubar-trigger",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)("focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex items-center rounded-sm px-2 py-1 text-sm font-medium outline-hidden select-none", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\packages\\design-system\\components\\ui\\menubar.tsx",
        lineNumber: 56,
        columnNumber: 5
    }, this);
}
_c5 = MenubarTrigger;
function MenubarContent({ className, align = "start", alignOffset = -4, sideOffset = 8, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MenubarPortal, {
        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_menubar__WEBPACK_IMPORTED_MODULE_3__.Content, {
            "data-slot": "menubar-content",
            align: align,
            alignOffset: alignOffset,
            sideOffset: sideOffset,
            className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[12rem] origin-(--radix-menubar-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-md", className),
            ...props
        }, void 0, false, {
            fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\packages\\design-system\\components\\ui\\menubar.tsx",
            lineNumber: 76,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\packages\\design-system\\components\\ui\\menubar.tsx",
        lineNumber: 75,
        columnNumber: 5
    }, this);
}
_c6 = MenubarContent;
function MenubarItem({ className, inset, variant = "default", ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_menubar__WEBPACK_IMPORTED_MODULE_3__.Item, {
        "data-slot": "menubar-item",
        "data-inset": inset,
        "data-variant": variant,
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\packages\\design-system\\components\\ui\\menubar.tsx",
        lineNumber: 101,
        columnNumber: 5
    }, this);
}
_c7 = MenubarItem;
function MenubarCheckboxItem({ className, children, checked, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_menubar__WEBPACK_IMPORTED_MODULE_3__.CheckboxItem, {
        "data-slot": "menubar-checkbox-item",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)("focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-xs py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4", className),
        checked: checked,
        ...props,
        children: [
            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("span", {
                className: "pointer-events-none absolute left-2 flex size-3.5 items-center justify-center",
                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_menubar__WEBPACK_IMPORTED_MODULE_3__.ItemIndicator, {
                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_ChevronRightIcon_CircleIcon_lucide_react__WEBPACK_IMPORTED_MODULE_4__["default"], {
                        className: "size-4"
                    }, void 0, false, {
                        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\packages\\design-system\\components\\ui\\menubar.tsx",
                        lineNumber: 132,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\packages\\design-system\\components\\ui\\menubar.tsx",
                    lineNumber: 131,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\packages\\design-system\\components\\ui\\menubar.tsx",
                lineNumber: 130,
                columnNumber: 7
            }, this),
            children
        ]
    }, void 0, true, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\packages\\design-system\\components\\ui\\menubar.tsx",
        lineNumber: 121,
        columnNumber: 5
    }, this);
}
_c8 = MenubarCheckboxItem;
function MenubarRadioItem({ className, children, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_menubar__WEBPACK_IMPORTED_MODULE_3__.RadioItem, {
        "data-slot": "menubar-radio-item",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)("focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-xs py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4", className),
        ...props,
        children: [
            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("span", {
                className: "pointer-events-none absolute left-2 flex size-3.5 items-center justify-center",
                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_menubar__WEBPACK_IMPORTED_MODULE_3__.ItemIndicator, {
                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_ChevronRightIcon_CircleIcon_lucide_react__WEBPACK_IMPORTED_MODULE_5__["default"], {
                        className: "size-2 fill-current"
                    }, void 0, false, {
                        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\packages\\design-system\\components\\ui\\menubar.tsx",
                        lineNumber: 156,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\packages\\design-system\\components\\ui\\menubar.tsx",
                    lineNumber: 155,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\packages\\design-system\\components\\ui\\menubar.tsx",
                lineNumber: 154,
                columnNumber: 7
            }, this),
            children
        ]
    }, void 0, true, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\packages\\design-system\\components\\ui\\menubar.tsx",
        lineNumber: 146,
        columnNumber: 5
    }, this);
}
_c9 = MenubarRadioItem;
function MenubarLabel({ className, inset, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_menubar__WEBPACK_IMPORTED_MODULE_3__.Label, {
        "data-slot": "menubar-label",
        "data-inset": inset,
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\packages\\design-system\\components\\ui\\menubar.tsx",
        lineNumber: 172,
        columnNumber: 5
    }, this);
}
_c10 = MenubarLabel;
function MenubarSeparator({ className, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_menubar__WEBPACK_IMPORTED_MODULE_3__.Separator, {
        "data-slot": "menubar-separator",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)("bg-border -mx-1 my-1 h-px", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\packages\\design-system\\components\\ui\\menubar.tsx",
        lineNumber: 189,
        columnNumber: 5
    }, this);
}
_c11 = MenubarSeparator;
function MenubarShortcut({ className, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("span", {
        "data-slot": "menubar-shortcut",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)("text-muted-foreground ml-auto text-xs tracking-widest", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\packages\\design-system\\components\\ui\\menubar.tsx",
        lineNumber: 202,
        columnNumber: 5
    }, this);
}
_c12 = MenubarShortcut;
function MenubarSub({ ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_menubar__WEBPACK_IMPORTED_MODULE_3__.Sub, {
        "data-slot": "menubar-sub",
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\packages\\design-system\\components\\ui\\menubar.tsx",
        lineNumber: 216,
        columnNumber: 10
    }, this);
}
_c13 = MenubarSub;
function MenubarSubTrigger({ className, inset, children, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_menubar__WEBPACK_IMPORTED_MODULE_3__.SubTrigger, {
        "data-slot": "menubar-sub-trigger",
        "data-inset": inset,
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)("focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-none select-none data-[inset]:pl-8", className),
        ...props,
        children: [
            children,
            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_ChevronRightIcon_CircleIcon_lucide_react__WEBPACK_IMPORTED_MODULE_6__["default"], {
                className: "ml-auto h-4 w-4"
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\packages\\design-system\\components\\ui\\menubar.tsx",
                lineNumber: 238,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\packages\\design-system\\components\\ui\\menubar.tsx",
        lineNumber: 228,
        columnNumber: 5
    }, this);
}
_c14 = MenubarSubTrigger;
function MenubarSubContent({ className, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_menubar__WEBPACK_IMPORTED_MODULE_3__.SubContent, {
        "data-slot": "menubar-sub-content",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-menubar-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\packages\\design-system\\components\\ui\\menubar.tsx",
        lineNumber: 248,
        columnNumber: 5
    }, this);
}
_c15 = MenubarSubContent;

Menubar.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "Menubar"
};
MenubarPortal.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "MenubarPortal"
};
MenubarMenu.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "MenubarMenu"
};
MenubarTrigger.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "MenubarTrigger"
};
MenubarContent.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "MenubarContent",
    "props": {
        "align": {
            "defaultValue": {
                "value": "\"start\"",
                "computed": false
            },
            "required": false
        },
        "alignOffset": {
            "defaultValue": {
                "value": "-4",
                "computed": false
            },
            "required": false
        },
        "sideOffset": {
            "defaultValue": {
                "value": "8",
                "computed": false
            },
            "required": false
        }
    }
};
MenubarGroup.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "MenubarGroup"
};
MenubarSeparator.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "MenubarSeparator"
};
MenubarLabel.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "MenubarLabel",
    "props": {
        "inset": {
            "required": false,
            "tsType": {
                "name": "boolean"
            },
            "description": ""
        }
    }
};
MenubarItem.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "MenubarItem",
    "props": {
        "inset": {
            "required": false,
            "tsType": {
                "name": "boolean"
            },
            "description": ""
        },
        "variant": {
            "required": false,
            "tsType": {
                "name": "union",
                "raw": "\"default\" | \"destructive\"",
                "elements": [
                    {
                        "name": "literal",
                        "value": "\"default\""
                    },
                    {
                        "name": "literal",
                        "value": "\"destructive\""
                    }
                ]
            },
            "description": "",
            "defaultValue": {
                "value": "\"default\"",
                "computed": false
            }
        }
    }
};
MenubarShortcut.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "MenubarShortcut"
};
MenubarCheckboxItem.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "MenubarCheckboxItem"
};
MenubarRadioGroup.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "MenubarRadioGroup"
};
MenubarRadioItem.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "MenubarRadioItem"
};
MenubarSub.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "MenubarSub"
};
MenubarSubTrigger.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "MenubarSubTrigger",
    "props": {
        "inset": {
            "required": false,
            "tsType": {
                "name": "boolean"
            },
            "description": ""
        }
    }
};
MenubarSubContent.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "MenubarSubContent"
};
var _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11, _c12, _c13, _c14, _c15;
__webpack_require__.$Refresh$.register(_c, "Menubar");
__webpack_require__.$Refresh$.register(_c1, "MenubarMenu");
__webpack_require__.$Refresh$.register(_c2, "MenubarGroup");
__webpack_require__.$Refresh$.register(_c3, "MenubarPortal");
__webpack_require__.$Refresh$.register(_c4, "MenubarRadioGroup");
__webpack_require__.$Refresh$.register(_c5, "MenubarTrigger");
__webpack_require__.$Refresh$.register(_c6, "MenubarContent");
__webpack_require__.$Refresh$.register(_c7, "MenubarItem");
__webpack_require__.$Refresh$.register(_c8, "MenubarCheckboxItem");
__webpack_require__.$Refresh$.register(_c9, "MenubarRadioItem");
__webpack_require__.$Refresh$.register(_c10, "MenubarLabel");
__webpack_require__.$Refresh$.register(_c11, "MenubarSeparator");
__webpack_require__.$Refresh$.register(_c12, "MenubarShortcut");
__webpack_require__.$Refresh$.register(_c13, "MenubarSub");
__webpack_require__.$Refresh$.register(_c14, "MenubarSubTrigger");
__webpack_require__.$Refresh$.register(_c15, "MenubarSubContent");


const $ReactRefreshModuleId$ = __webpack_require__.$Refresh$.moduleId;
const $ReactRefreshCurrentExports$ = __react_refresh_utils__.getModuleExports(
	$ReactRefreshModuleId$
);

function $ReactRefreshModuleRuntime$(exports) {
	if (true) {
		let errorOverlay;
		if (true) {
			errorOverlay = false;
		}
		let testMode;
		if (typeof __react_refresh_test__ !== 'undefined') {
			testMode = __react_refresh_test__;
		}
		return __react_refresh_utils__.executeRuntime(
			exports,
			$ReactRefreshModuleId$,
			module.hot,
			errorOverlay,
			testMode
		);
	}
}

if (typeof Promise !== 'undefined' && $ReactRefreshCurrentExports$ instanceof Promise) {
	$ReactRefreshCurrentExports$.then($ReactRefreshModuleRuntime$);
} else {
	$ReactRefreshModuleRuntime$($ReactRefreshCurrentExports$);
}

/***/ }),

/***/ "./stories/menubar.stories.tsx":
/*!*************************************!*\
  !*** ./stories/menubar.stories.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Default: () => (/* binding */ Default),
/* harmony export */   WithCheckboxItems: () => (/* binding */ WithCheckboxItems),
/* harmony export */   WithRadioItems: () => (/* binding */ WithRadioItems),
/* harmony export */   WithSubmenu: () => (/* binding */ WithSubmenu),
/* harmony export */   __namedExportsOrder: () => (/* binding */ __namedExportsOrder),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/jsx-dev-runtime.js");
/* harmony import */ var _repo_design_system_components_ui_menubar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @repo/design-system/components/ui/menubar */ "../../packages/design-system/components/ui/menubar.tsx");
/* provided dependency */ var __react_refresh_utils__ = __webpack_require__(/*! ../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js */ "../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js");
__webpack_require__.$Refresh$.runtime = __webpack_require__(/*! ../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js */ "../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js");



/**
 * A visually persistent menu common in desktop applications that provides
 * quick access to a consistent set of commands.
 */
const meta = {
  title: 'ui/Menubar',
  component: _repo_design_system_components_ui_menubar__WEBPACK_IMPORTED_MODULE_1__.Menubar,
  tags: ['autodocs'],
  argTypes: {},
  render: args => /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_menubar__WEBPACK_IMPORTED_MODULE_1__.Menubar, {
    ...args,
    children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_menubar__WEBPACK_IMPORTED_MODULE_1__.MenubarMenu, {
      children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_menubar__WEBPACK_IMPORTED_MODULE_1__.MenubarTrigger, {
        children: "File"
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\menubar.stories.tsx",
        lineNumber: 34,
        columnNumber: 9
      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_menubar__WEBPACK_IMPORTED_MODULE_1__.MenubarContent, {
        children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_menubar__WEBPACK_IMPORTED_MODULE_1__.MenubarItem, {
          children: ["New Tab ", /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_menubar__WEBPACK_IMPORTED_MODULE_1__.MenubarShortcut, {
            children: "⌘T"
          }, void 0, false, {
            fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\menubar.stories.tsx",
            lineNumber: 37,
            columnNumber: 21
          }, undefined)]
        }, void 0, true, {
          fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\menubar.stories.tsx",
          lineNumber: 36,
          columnNumber: 11
        }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_menubar__WEBPACK_IMPORTED_MODULE_1__.MenubarItem, {
          children: "New Window"
        }, void 0, false, {
          fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\menubar.stories.tsx",
          lineNumber: 39,
          columnNumber: 11
        }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_menubar__WEBPACK_IMPORTED_MODULE_1__.MenubarSeparator, {}, void 0, false, {
          fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\menubar.stories.tsx",
          lineNumber: 40,
          columnNumber: 11
        }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_menubar__WEBPACK_IMPORTED_MODULE_1__.MenubarItem, {
          disabled: true,
          children: "Share"
        }, void 0, false, {
          fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\menubar.stories.tsx",
          lineNumber: 41,
          columnNumber: 11
        }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_menubar__WEBPACK_IMPORTED_MODULE_1__.MenubarSeparator, {}, void 0, false, {
          fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\menubar.stories.tsx",
          lineNumber: 42,
          columnNumber: 11
        }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_menubar__WEBPACK_IMPORTED_MODULE_1__.MenubarItem, {
          children: "Print"
        }, void 0, false, {
          fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\menubar.stories.tsx",
          lineNumber: 43,
          columnNumber: 11
        }, undefined)]
      }, void 0, true, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\menubar.stories.tsx",
        lineNumber: 35,
        columnNumber: 9
      }, undefined)]
    }, void 0, true, {
      fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\menubar.stories.tsx",
      lineNumber: 33,
      columnNumber: 7
    }, undefined)
  }, void 0, false, {
    fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\menubar.stories.tsx",
    lineNumber: 32,
    columnNumber: 5
  }, undefined),
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: "A visually persistent menu common in desktop applications that provides\r\nquick access to a consistent set of commands."
      }
    }
  }
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (meta);
/**
 * The default form of the menubar.
 */
const Default = {};
/**
 * A menubar with a submenu.
 */
const WithSubmenu = {
  render: args => /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_menubar__WEBPACK_IMPORTED_MODULE_1__.Menubar, {
    ...args,
    children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_menubar__WEBPACK_IMPORTED_MODULE_1__.MenubarMenu, {
      children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_menubar__WEBPACK_IMPORTED_MODULE_1__.MenubarTrigger, {
        children: "Actions"
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\menubar.stories.tsx",
        lineNumber: 69,
        columnNumber: 9
      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_menubar__WEBPACK_IMPORTED_MODULE_1__.MenubarContent, {
        children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_menubar__WEBPACK_IMPORTED_MODULE_1__.MenubarItem, {
          children: "Download"
        }, void 0, false, {
          fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\menubar.stories.tsx",
          lineNumber: 71,
          columnNumber: 11
        }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_menubar__WEBPACK_IMPORTED_MODULE_1__.MenubarSub, {
          children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_menubar__WEBPACK_IMPORTED_MODULE_1__.MenubarSubTrigger, {
            children: "Share"
          }, void 0, false, {
            fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\menubar.stories.tsx",
            lineNumber: 73,
            columnNumber: 13
          }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_menubar__WEBPACK_IMPORTED_MODULE_1__.MenubarSubContent, {
            children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_menubar__WEBPACK_IMPORTED_MODULE_1__.MenubarItem, {
              children: "Email link"
            }, void 0, false, {
              fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\menubar.stories.tsx",
              lineNumber: 75,
              columnNumber: 15
            }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_menubar__WEBPACK_IMPORTED_MODULE_1__.MenubarItem, {
              children: "Messages"
            }, void 0, false, {
              fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\menubar.stories.tsx",
              lineNumber: 76,
              columnNumber: 15
            }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_menubar__WEBPACK_IMPORTED_MODULE_1__.MenubarItem, {
              children: "Notes"
            }, void 0, false, {
              fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\menubar.stories.tsx",
              lineNumber: 77,
              columnNumber: 15
            }, undefined)]
          }, void 0, true, {
            fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\menubar.stories.tsx",
            lineNumber: 74,
            columnNumber: 13
          }, undefined)]
        }, void 0, true, {
          fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\menubar.stories.tsx",
          lineNumber: 72,
          columnNumber: 11
        }, undefined)]
      }, void 0, true, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\menubar.stories.tsx",
        lineNumber: 70,
        columnNumber: 9
      }, undefined)]
    }, void 0, true, {
      fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\menubar.stories.tsx",
      lineNumber: 68,
      columnNumber: 7
    }, undefined)
  }, void 0, false, {
    fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\menubar.stories.tsx",
    lineNumber: 67,
    columnNumber: 5
  }, undefined)
};
/**
 * A menubar with radio items.
 */
const WithRadioItems = {
  render: args => /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_menubar__WEBPACK_IMPORTED_MODULE_1__.Menubar, {
    ...args,
    children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_menubar__WEBPACK_IMPORTED_MODULE_1__.MenubarMenu, {
      children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_menubar__WEBPACK_IMPORTED_MODULE_1__.MenubarTrigger, {
        children: "View"
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\menubar.stories.tsx",
        lineNumber: 93,
        columnNumber: 9
      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_menubar__WEBPACK_IMPORTED_MODULE_1__.MenubarContent, {
        children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_menubar__WEBPACK_IMPORTED_MODULE_1__.MenubarLabel, {
          inset: true,
          children: "Device Size"
        }, void 0, false, {
          fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\menubar.stories.tsx",
          lineNumber: 95,
          columnNumber: 11
        }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_menubar__WEBPACK_IMPORTED_MODULE_1__.MenubarRadioGroup, {
          value: "md",
          children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_menubar__WEBPACK_IMPORTED_MODULE_1__.MenubarRadioItem, {
            value: "sm",
            children: "Small"
          }, void 0, false, {
            fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\menubar.stories.tsx",
            lineNumber: 97,
            columnNumber: 13
          }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_menubar__WEBPACK_IMPORTED_MODULE_1__.MenubarRadioItem, {
            value: "md",
            children: "Medium"
          }, void 0, false, {
            fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\menubar.stories.tsx",
            lineNumber: 98,
            columnNumber: 13
          }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_menubar__WEBPACK_IMPORTED_MODULE_1__.MenubarRadioItem, {
            value: "lg",
            children: "Large"
          }, void 0, false, {
            fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\menubar.stories.tsx",
            lineNumber: 99,
            columnNumber: 13
          }, undefined)]
        }, void 0, true, {
          fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\menubar.stories.tsx",
          lineNumber: 96,
          columnNumber: 11
        }, undefined)]
      }, void 0, true, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\menubar.stories.tsx",
        lineNumber: 94,
        columnNumber: 9
      }, undefined)]
    }, void 0, true, {
      fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\menubar.stories.tsx",
      lineNumber: 92,
      columnNumber: 7
    }, undefined)
  }, void 0, false, {
    fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\menubar.stories.tsx",
    lineNumber: 91,
    columnNumber: 5
  }, undefined)
};
/**
 * A menubar with checkbox items.
 */
const WithCheckboxItems = {
  render: args => /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_menubar__WEBPACK_IMPORTED_MODULE_1__.Menubar, {
    ...args,
    children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_menubar__WEBPACK_IMPORTED_MODULE_1__.MenubarMenu, {
      children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_menubar__WEBPACK_IMPORTED_MODULE_1__.MenubarTrigger, {
        children: "Filters"
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\menubar.stories.tsx",
        lineNumber: 114,
        columnNumber: 9
      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_menubar__WEBPACK_IMPORTED_MODULE_1__.MenubarContent, {
        children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_menubar__WEBPACK_IMPORTED_MODULE_1__.MenubarItem, {
          children: "Show All"
        }, void 0, false, {
          fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\menubar.stories.tsx",
          lineNumber: 116,
          columnNumber: 11
        }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_menubar__WEBPACK_IMPORTED_MODULE_1__.MenubarGroup, {
          children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_menubar__WEBPACK_IMPORTED_MODULE_1__.MenubarCheckboxItem, {
            checked: true,
            children: "Unread"
          }, void 0, false, {
            fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\menubar.stories.tsx",
            lineNumber: 118,
            columnNumber: 13
          }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_menubar__WEBPACK_IMPORTED_MODULE_1__.MenubarCheckboxItem, {
            checked: true,
            children: "Important"
          }, void 0, false, {
            fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\menubar.stories.tsx",
            lineNumber: 119,
            columnNumber: 13
          }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_menubar__WEBPACK_IMPORTED_MODULE_1__.MenubarCheckboxItem, {
            children: "Flagged"
          }, void 0, false, {
            fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\menubar.stories.tsx",
            lineNumber: 120,
            columnNumber: 13
          }, undefined)]
        }, void 0, true, {
          fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\menubar.stories.tsx",
          lineNumber: 117,
          columnNumber: 11
        }, undefined)]
      }, void 0, true, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\menubar.stories.tsx",
        lineNumber: 115,
        columnNumber: 9
      }, undefined)]
    }, void 0, true, {
      fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\menubar.stories.tsx",
      lineNumber: 113,
      columnNumber: 7
    }, undefined)
  }, void 0, false, {
    fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\CubentWeb\\apps\\storybook\\stories\\menubar.stories.tsx",
    lineNumber: 112,
    columnNumber: 5
  }, undefined)
};
;
const __namedExportsOrder = ["Default", "WithSubmenu", "WithRadioItems", "WithCheckboxItems"];
Default.parameters = {
  ...Default.parameters,
  docs: {
    ...Default.parameters?.docs,
    source: {
      originalSource: "{}",
      ...Default.parameters?.docs?.source
    },
    description: {
      story: "The default form of the menubar.",
      ...Default.parameters?.docs?.description
    }
  }
};
WithSubmenu.parameters = {
  ...WithSubmenu.parameters,
  docs: {
    ...WithSubmenu.parameters?.docs,
    source: {
      originalSource: "{\n  render: args => <Menubar {...args}>\r\n      <MenubarMenu>\r\n        <MenubarTrigger>Actions</MenubarTrigger>\r\n        <MenubarContent>\r\n          <MenubarItem>Download</MenubarItem>\r\n          <MenubarSub>\r\n            <MenubarSubTrigger>Share</MenubarSubTrigger>\r\n            <MenubarSubContent>\r\n              <MenubarItem>Email link</MenubarItem>\r\n              <MenubarItem>Messages</MenubarItem>\r\n              <MenubarItem>Notes</MenubarItem>\r\n            </MenubarSubContent>\r\n          </MenubarSub>\r\n        </MenubarContent>\r\n      </MenubarMenu>\r\n    </Menubar>\n}",
      ...WithSubmenu.parameters?.docs?.source
    },
    description: {
      story: "A menubar with a submenu.",
      ...WithSubmenu.parameters?.docs?.description
    }
  }
};
WithRadioItems.parameters = {
  ...WithRadioItems.parameters,
  docs: {
    ...WithRadioItems.parameters?.docs,
    source: {
      originalSource: "{\n  render: args => <Menubar {...args}>\r\n      <MenubarMenu>\r\n        <MenubarTrigger>View</MenubarTrigger>\r\n        <MenubarContent>\r\n          <MenubarLabel inset>Device Size</MenubarLabel>\r\n          <MenubarRadioGroup value=\"md\">\r\n            <MenubarRadioItem value=\"sm\">Small</MenubarRadioItem>\r\n            <MenubarRadioItem value=\"md\">Medium</MenubarRadioItem>\r\n            <MenubarRadioItem value=\"lg\">Large</MenubarRadioItem>\r\n          </MenubarRadioGroup>\r\n        </MenubarContent>\r\n      </MenubarMenu>\r\n    </Menubar>\n}",
      ...WithRadioItems.parameters?.docs?.source
    },
    description: {
      story: "A menubar with radio items.",
      ...WithRadioItems.parameters?.docs?.description
    }
  }
};
WithCheckboxItems.parameters = {
  ...WithCheckboxItems.parameters,
  docs: {
    ...WithCheckboxItems.parameters?.docs,
    source: {
      originalSource: "{\n  render: args => <Menubar {...args}>\r\n      <MenubarMenu>\r\n        <MenubarTrigger>Filters</MenubarTrigger>\r\n        <MenubarContent>\r\n          <MenubarItem>Show All</MenubarItem>\r\n          <MenubarGroup>\r\n            <MenubarCheckboxItem checked>Unread</MenubarCheckboxItem>\r\n            <MenubarCheckboxItem checked>Important</MenubarCheckboxItem>\r\n            <MenubarCheckboxItem>Flagged</MenubarCheckboxItem>\r\n          </MenubarGroup>\r\n        </MenubarContent>\r\n      </MenubarMenu>\r\n    </Menubar>\n}",
      ...WithCheckboxItems.parameters?.docs?.source
    },
    description: {
      story: "A menubar with checkbox items.",
      ...WithCheckboxItems.parameters?.docs?.description
    }
  }
};

const $ReactRefreshModuleId$ = __webpack_require__.$Refresh$.moduleId;
const $ReactRefreshCurrentExports$ = __react_refresh_utils__.getModuleExports(
	$ReactRefreshModuleId$
);

function $ReactRefreshModuleRuntime$(exports) {
	if (true) {
		let errorOverlay;
		if (true) {
			errorOverlay = false;
		}
		let testMode;
		if (typeof __react_refresh_test__ !== 'undefined') {
			testMode = __react_refresh_test__;
		}
		return __react_refresh_utils__.executeRuntime(
			exports,
			$ReactRefreshModuleId$,
			module.hot,
			errorOverlay,
			testMode
		);
	}
}

if (typeof Promise !== 'undefined' && $ReactRefreshCurrentExports$ instanceof Promise) {
	$ReactRefreshCurrentExports$.then($ReactRefreshModuleRuntime$);
} else {
	$ReactRefreshModuleRuntime$($ReactRefreshCurrentExports$);
}

/***/ })

}]);
//# sourceMappingURL=menubar-stories.iframe.bundle.js.map