export function getAttemptCompletionDescription(): string {
	return `## attempt_completion
Description: Use this tool AFTER you have completed the user's task to signal that you're finished. This tool comes AFTER doing the work (answering questions, writing code, providing explanations, etc.), not instead of it.

CRITICAL WORKFLOW:
1. FIRST: Complete the user's task (answer their question, write code, provide explanations)
2. THEN: Use this tool to signal completion

This tool ONLY signals completion - users will only see feedback icons (thumbs up/down, copy, etc.). The result parameter is for internal tracking only.

Optionally you may provide a CLI command to showcase the result of your work. The user may respond with feedback if they are not satisfied with the result, which you can use to make improvements and try again.

IMPORTANT NOTES:
- For simple greetings, acknowledgments, or responses that don't involve tools: Use this tool immediately after responding
- For tasks involving other tools: Only use this tool after confirming from the user that any previous tool uses were successful
- If no other tools were used, you can use this tool right away 
Parameters:
- result: (required) A brief internal summary for tracking purposes only. This text is NEVER shown to the user - only feedback icons appear. Keep it short and factual. Do not write explanatory text or user-facing messages here.
Usage:
<attempt_completion>
<result>
Brief internal summary (NEVER shown to user)
</result>
</attempt_completion>

Example: After answering a user's question about real estate, signal completion
<attempt_completion>
<result>
Provided real estate explanation
</result>
</attempt_completion>

Example: After responding to "great", signal completion
<attempt_completion>
<result>
Acknowledged user feedback
</result>
</attempt_completion>

Example: After writing code, signal completion
<attempt_completion>
<result>
Created HTML website
</result>
</attempt_completion>

Example: After responding to "hi" or any sort of greeting, signal completion
<attempt_completion>
<result>
Acknowledged user greeting
</result>
</attempt_completion>

REMEMBER: Use this tool AFTER completing the work, not instead of doing it. Users see only feedback icons and optional command output.`
}
