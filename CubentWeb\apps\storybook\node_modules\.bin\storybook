#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/proc/cygdrive/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/CubentWeb/node_modules/.pnpm/storybook@8.6.14_bufferutil@4.0.9_prettier@3.5.3/node_modules/storybook/bin/node_modules:/proc/cygdrive/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/CubentWeb/node_modules/.pnpm/storybook@8.6.14_bufferutil@4.0.9_prettier@3.5.3/node_modules/storybook/node_modules:/proc/cygdrive/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/CubentWeb/node_modules/.pnpm/storybook@8.6.14_bufferutil@4.0.9_prettier@3.5.3/node_modules:/proc/cygdrive/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/CubentWeb/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/proc/cygdrive/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/CubentWeb/node_modules/.pnpm/storybook@8.6.14_bufferutil@4.0.9_prettier@3.5.3/node_modules/storybook/bin/node_modules:/proc/cygdrive/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/CubentWeb/node_modules/.pnpm/storybook@8.6.14_bufferutil@4.0.9_prettier@3.5.3/node_modules/storybook/node_modules:/proc/cygdrive/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/CubentWeb/node_modules/.pnpm/storybook@8.6.14_bufferutil@4.0.9_prettier@3.5.3/node_modules:/proc/cygdrive/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/CubentWeb/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../storybook/bin/index.cjs" "$@"
else
  exec node  "$basedir/../storybook/bin/index.cjs" "$@"
fi
