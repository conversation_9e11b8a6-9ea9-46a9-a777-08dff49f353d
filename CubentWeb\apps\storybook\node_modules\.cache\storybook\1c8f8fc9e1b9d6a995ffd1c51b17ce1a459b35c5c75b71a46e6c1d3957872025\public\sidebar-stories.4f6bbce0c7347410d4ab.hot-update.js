"use strict";
self["webpackHotUpdatestorybook"]("sidebar-stories",{

/***/ "../../packages/design-system/components/ui/sidebar.tsx":
/*!**************************************************************!*\
  !*** ../../packages/design-system/components/ui/sidebar.tsx ***!
  \**************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Sidebar: () => (/* binding */ Sidebar),
/* harmony export */   SidebarContent: () => (/* binding */ SidebarContent),
/* harmony export */   SidebarFooter: () => (/* binding */ SidebarFooter),
/* harmony export */   SidebarGroup: () => (/* binding */ SidebarGroup),
/* harmony export */   SidebarGroupAction: () => (/* binding */ SidebarGroupAction),
/* harmony export */   SidebarGroupContent: () => (/* binding */ SidebarGroupContent),
/* harmony export */   SidebarGroupLabel: () => (/* binding */ SidebarGroupLabel),
/* harmony export */   SidebarHeader: () => (/* binding */ SidebarHeader),
/* harmony export */   SidebarInput: () => (/* binding */ SidebarInput),
/* harmony export */   SidebarInset: () => (/* binding */ SidebarInset),
/* harmony export */   SidebarMenu: () => (/* binding */ SidebarMenu),
/* harmony export */   SidebarMenuAction: () => (/* binding */ SidebarMenuAction),
/* harmony export */   SidebarMenuBadge: () => (/* binding */ SidebarMenuBadge),
/* harmony export */   SidebarMenuButton: () => (/* binding */ SidebarMenuButton),
/* harmony export */   SidebarMenuItem: () => (/* binding */ SidebarMenuItem),
/* harmony export */   SidebarMenuSkeleton: () => (/* binding */ SidebarMenuSkeleton),
/* harmony export */   SidebarMenuSub: () => (/* binding */ SidebarMenuSub),
/* harmony export */   SidebarMenuSubButton: () => (/* binding */ SidebarMenuSubButton),
/* harmony export */   SidebarMenuSubItem: () => (/* binding */ SidebarMenuSubItem),
/* harmony export */   SidebarProvider: () => (/* binding */ SidebarProvider),
/* harmony export */   SidebarRail: () => (/* binding */ SidebarRail),
/* harmony export */   SidebarSeparator: () => (/* binding */ SidebarSeparator),
/* harmony export */   SidebarTrigger: () => (/* binding */ SidebarTrigger),
/* harmony export */   useSidebar: () => (/* binding */ useSidebar)
/* harmony export */ });
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/jsx-dev-runtime.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @radix-ui/react-slot */ "../../node_modules/.pnpm/@radix-ui+react-slot@1.2.3_@types+react@19.1.5_react@19.1.0/node_modules/@radix-ui/react-slot/dist/index.mjs");
/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! class-variance-authority */ "../../node_modules/.pnpm/class-variance-authority@0.7.1/node_modules/class-variance-authority/dist/index.mjs");
/* harmony import */ var _barrel_optimize_names_PanelLeftIcon_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=PanelLeftIcon!=!lucide-react */ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/panel-left.js");
/* harmony import */ var _repo_design_system_hooks_use_mobile__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @repo/design-system/hooks/use-mobile */ "../../packages/design-system/hooks/use-mobile.ts");
/* harmony import */ var _repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @repo/design-system/lib/utils */ "../../packages/design-system/lib/utils.ts");
/* harmony import */ var _repo_design_system_components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @repo/design-system/components/ui/button */ "../../packages/design-system/components/ui/button.tsx");
/* harmony import */ var _repo_design_system_components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @repo/design-system/components/ui/input */ "../../packages/design-system/components/ui/input.tsx");
/* harmony import */ var _repo_design_system_components_ui_separator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @repo/design-system/components/ui/separator */ "../../packages/design-system/components/ui/separator.tsx");
/* harmony import */ var _repo_design_system_components_ui_sheet__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @repo/design-system/components/ui/sheet */ "../../packages/design-system/components/ui/sheet.tsx");
/* harmony import */ var _repo_design_system_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @repo/design-system/components/ui/skeleton */ "../../packages/design-system/components/ui/skeleton.tsx");
/* harmony import */ var _repo_design_system_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @repo/design-system/components/ui/tooltip */ "../../packages/design-system/components/ui/tooltip.tsx");
/* provided dependency */ var __react_refresh_utils__ = __webpack_require__(/*! ../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js */ "../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js");
__webpack_require__.$Refresh$.runtime = __webpack_require__(/*! ../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js */ "../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js");


var _s = __webpack_require__.$Refresh$.signature(), _s1 = __webpack_require__.$Refresh$.signature(), _s2 = __webpack_require__.$Refresh$.signature(), _s3 = __webpack_require__.$Refresh$.signature(), _s4 = __webpack_require__.$Refresh$.signature(), _s5 = __webpack_require__.$Refresh$.signature(), _s6 = __webpack_require__.$Refresh$.signature();
"use client";












const SIDEBAR_COOKIE_NAME = "sidebar_state";
const SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7;
const SIDEBAR_WIDTH = "14rem";
const SIDEBAR_WIDTH_MOBILE = "18rem";
const SIDEBAR_WIDTH_ICON = "3rem";
const SIDEBAR_KEYBOARD_SHORTCUT = "b";
const SidebarContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createContext(null);
function useSidebar() {
    _s();
    const context = react__WEBPACK_IMPORTED_MODULE_1__.useContext(SidebarContext);
    if (!context) {
        throw new Error("useSidebar must be used within a SidebarProvider.");
    }
    return context;
}
_s(useSidebar, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
function SidebarProvider({ defaultOpen = true, open: openProp, onOpenChange: setOpenProp, className, style, children, ...props }) {
    _s1();
    const isMobile = (0,_repo_design_system_hooks_use_mobile__WEBPACK_IMPORTED_MODULE_2__.useIsMobile)();
    const [openMobile, setOpenMobile] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);
    // This is the internal state of the sidebar.
    // We use openProp and setOpenProp for control from outside the component.
    const [_open, _setOpen] = react__WEBPACK_IMPORTED_MODULE_1__.useState(defaultOpen);
    const open = openProp !== null && openProp !== void 0 ? openProp : _open;
    const setOpen = react__WEBPACK_IMPORTED_MODULE_1__.useCallback({
        "SidebarProvider.useCallback[setOpen]": (value)=>{
            const openState = typeof value === "function" ? value(open) : value;
            if (setOpenProp) {
                setOpenProp(openState);
            } else {
                _setOpen(openState);
            }
            // This sets the cookie to keep the sidebar state.
            document.cookie = `${SIDEBAR_COOKIE_NAME}=${openState}; path=/; max-age=${SIDEBAR_COOKIE_MAX_AGE}`;
        }
    }["SidebarProvider.useCallback[setOpen]"], [
        setOpenProp,
        open
    ]);
    // Helper to toggle the sidebar.
    const toggleSidebar = react__WEBPACK_IMPORTED_MODULE_1__.useCallback({
        "SidebarProvider.useCallback[toggleSidebar]": ()=>{
            return isMobile ? setOpenMobile({
                "SidebarProvider.useCallback[toggleSidebar]": (open)=>!open
            }["SidebarProvider.useCallback[toggleSidebar]"]) : setOpen({
                "SidebarProvider.useCallback[toggleSidebar]": (open)=>!open
            }["SidebarProvider.useCallback[toggleSidebar]"]);
        }
    }["SidebarProvider.useCallback[toggleSidebar]"], [
        isMobile,
        setOpen,
        setOpenMobile
    ]);
    // Adds a keyboard shortcut to toggle the sidebar.
    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({
        "SidebarProvider.useEffect": ()=>{
            const handleKeyDown = {
                "SidebarProvider.useEffect.handleKeyDown": (event)=>{
                    if (event.key === SIDEBAR_KEYBOARD_SHORTCUT && (event.metaKey || event.ctrlKey)) {
                        event.preventDefault();
                        toggleSidebar();
                    }
                }
            }["SidebarProvider.useEffect.handleKeyDown"];
            window.addEventListener("keydown", handleKeyDown);
            return ({
                "SidebarProvider.useEffect": ()=>window.removeEventListener("keydown", handleKeyDown)
            })["SidebarProvider.useEffect"];
        }
    }["SidebarProvider.useEffect"], [
        toggleSidebar
    ]);
    // We add a state so that we can do data-state="expanded" or "collapsed".
    // This makes it easier to style the sidebar with Tailwind classes.
    const state = open ? "expanded" : "collapsed";
    const contextValue = react__WEBPACK_IMPORTED_MODULE_1__.useMemo({
        "SidebarProvider.useMemo[contextValue]": ()=>({
                state,
                open,
                setOpen,
                isMobile,
                openMobile,
                setOpenMobile,
                toggleSidebar
            })
    }["SidebarProvider.useMemo[contextValue]"], [
        state,
        open,
        setOpen,
        isMobile,
        openMobile,
        setOpenMobile,
        toggleSidebar
    ]);
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarContext.Provider, {
        value: contextValue,
        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_9__.TooltipProvider, {
            delayDuration: 0,
            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
                "data-slot": "sidebar-wrapper",
                style: {
                    "--sidebar-width": SIDEBAR_WIDTH,
                    "--sidebar-width-icon": SIDEBAR_WIDTH_ICON,
                    ...style
                },
                className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)("group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full", className),
                ...props,
                children: children
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
                lineNumber: 132,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
            lineNumber: 131,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
        lineNumber: 130,
        columnNumber: 5
    }, this);
}
_s1(SidebarProvider, "QSOkjq1AvKFJW5+zwiK52jPX7zI=", false, function() {
    return [
        _repo_design_system_hooks_use_mobile__WEBPACK_IMPORTED_MODULE_2__.useIsMobile
    ];
});
_c = SidebarProvider;
function Sidebar({ side = "left", variant = "sidebar", collapsible = "offcanvas", className, children, ...props }) {
    _s2();
    const { isMobile, state, openMobile, setOpenMobile } = useSidebar();
    if (collapsible === "none") {
        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
            "data-slot": "sidebar",
            className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)("bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col", className),
            ...props,
            children: children
        }, void 0, false, {
            fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
            lineNumber: 170,
            columnNumber: 7
        }, this);
    }
    if (isMobile) {
        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_sheet__WEBPACK_IMPORTED_MODULE_7__.Sheet, {
            open: openMobile,
            onOpenChange: setOpenMobile,
            ...props,
            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_sheet__WEBPACK_IMPORTED_MODULE_7__.SheetContent, {
                "data-sidebar": "sidebar",
                "data-slot": "sidebar",
                "data-mobile": "true",
                className: "bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden",
                style: {
                    "--sidebar-width": SIDEBAR_WIDTH_MOBILE
                },
                side: side,
                children: [
                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_sheet__WEBPACK_IMPORTED_MODULE_7__.SheetHeader, {
                        className: "sr-only",
                        children: [
                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_sheet__WEBPACK_IMPORTED_MODULE_7__.SheetTitle, {
                                children: "Sidebar"
                            }, void 0, false, {
                                fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
                                lineNumber: 199,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_sheet__WEBPACK_IMPORTED_MODULE_7__.SheetDescription, {
                                children: "Displays the mobile sidebar."
                            }, void 0, false, {
                                fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
                                lineNumber: 200,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
                        lineNumber: 198,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
                        className: "flex h-full w-full flex-col",
                        children: children
                    }, void 0, false, {
                        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
                        lineNumber: 202,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
                lineNumber: 186,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
            lineNumber: 185,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
        className: "group peer text-sidebar-foreground hidden md:block",
        "data-state": state,
        "data-collapsible": state === "collapsed" ? collapsible : "",
        "data-variant": variant,
        "data-side": side,
        "data-slot": "sidebar",
        children: [
            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
                "data-slot": "sidebar-gap",
                className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)("relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear", "group-data-[collapsible=offcanvas]:w-0", "group-data-[side=right]:rotate-180", variant === "floating" || variant === "inset" ? "group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]" : "group-data-[collapsible=icon]:w-(--sidebar-width-icon)")
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
                lineNumber: 218,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
                "data-slot": "sidebar-container",
                className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)("fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex", side === "left" ? "left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]" : "right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]", // Adjust the padding for floating and inset variants.
                variant === "floating" || variant === "inset" ? "p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]" : "group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l", className),
                ...props,
                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
                    "data-sidebar": "sidebar",
                    "data-slot": "sidebar-inner",
                    className: "bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm",
                    children: children
                }, void 0, false, {
                    fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
                    lineNumber: 244,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
                lineNumber: 229,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
        lineNumber: 209,
        columnNumber: 5
    }, this);
}
_s2(Sidebar, "hAL3+uRFwO9tnbDK50BUE5wZ71s=", false, function() {
    return [
        useSidebar
    ];
});
_c1 = Sidebar;
function SidebarTrigger({ className, onClick, ...props }) {
    _s3();
    const { toggleSidebar } = useSidebar();
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {
        "data-sidebar": "trigger",
        "data-slot": "sidebar-trigger",
        variant: "ghost",
        size: "icon",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)("size-7", className),
        onClick: (event)=>{
            onClick === null || onClick === void 0 ? void 0 : onClick(event);
            toggleSidebar();
        },
        ...props,
        children: [
            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PanelLeftIcon_lucide_react__WEBPACK_IMPORTED_MODULE_10__["default"], {}, void 0, false, {
                fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
                lineNumber: 276,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("span", {
                className: "sr-only",
                children: "Toggle Sidebar"
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
                lineNumber: 277,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
        lineNumber: 264,
        columnNumber: 5
    }, this);
}
_s3(SidebarTrigger, "dRnjPhQbCChcVGr4xvQkpNxnqyg=", false, function() {
    return [
        useSidebar
    ];
});
_c2 = SidebarTrigger;
function SidebarRail({ className, ...props }) {
    _s4();
    const { toggleSidebar } = useSidebar();
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("button", {
        "data-sidebar": "rail",
        "data-slot": "sidebar-rail",
        "aria-label": "Toggle Sidebar",
        tabIndex: -1,
        onClick: toggleSidebar,
        title: "Toggle Sidebar",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)("hover:after:bg-sidebar-border absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear group-data-[side=left]:-right-4 group-data-[side=right]:left-0 after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] sm:flex", "in-data-[side=left]:cursor-w-resize in-data-[side=right]:cursor-e-resize", "[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize", "hover:group-data-[collapsible=offcanvas]:bg-sidebar group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full", "[[data-side=left][data-collapsible=offcanvas]_&]:-right-2", "[[data-side=right][data-collapsible=offcanvas]_&]:-left-2", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
        lineNumber: 286,
        columnNumber: 5
    }, this);
}
_s4(SidebarRail, "dRnjPhQbCChcVGr4xvQkpNxnqyg=", false, function() {
    return [
        useSidebar
    ];
});
_c3 = SidebarRail;
function SidebarInset({ className, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("main", {
        "data-slot": "sidebar-inset",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)("bg-background relative flex w-full flex-1 flex-col", "md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
        lineNumber: 309,
        columnNumber: 5
    }, this);
}
_c4 = SidebarInset;
function SidebarInput({ className, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {
        "data-slot": "sidebar-input",
        "data-sidebar": "input",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)("bg-background h-8 w-full shadow-none", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
        lineNumber: 326,
        columnNumber: 5
    }, this);
}
_c5 = SidebarInput;
function SidebarHeader({ className, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
        "data-slot": "sidebar-header",
        "data-sidebar": "header",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)("flex flex-col gap-2 p-2", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
        lineNumber: 337,
        columnNumber: 5
    }, this);
}
_c6 = SidebarHeader;
function SidebarFooter({ className, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
        "data-slot": "sidebar-footer",
        "data-sidebar": "footer",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)("flex flex-col gap-2 p-2", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
        lineNumber: 348,
        columnNumber: 5
    }, this);
}
_c7 = SidebarFooter;
function SidebarSeparator({ className, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_separator__WEBPACK_IMPORTED_MODULE_6__.Separator, {
        "data-slot": "sidebar-separator",
        "data-sidebar": "separator",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)("bg-sidebar-border mx-2 w-auto", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
        lineNumber: 362,
        columnNumber: 5
    }, this);
}
_c8 = SidebarSeparator;
function SidebarContent({ className, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
        "data-slot": "sidebar-content",
        "data-sidebar": "content",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
        lineNumber: 373,
        columnNumber: 5
    }, this);
}
_c9 = SidebarContent;
function SidebarGroup({ className, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
        "data-slot": "sidebar-group",
        "data-sidebar": "group",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)("relative flex w-full min-w-0 flex-col p-2", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
        lineNumber: 387,
        columnNumber: 5
    }, this);
}
_c10 = SidebarGroup;
function SidebarGroupLabel({ className, asChild = false, ...props }) {
    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_11__.Slot : "div";
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {
        "data-slot": "sidebar-group-label",
        "data-sidebar": "group-label",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)("text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0", "group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
        lineNumber: 404,
        columnNumber: 5
    }, this);
}
_c11 = SidebarGroupLabel;
function SidebarGroupAction({ className, asChild = false, ...props }) {
    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_11__.Slot : "button";
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {
        "data-slot": "sidebar-group-action",
        "data-sidebar": "group-action",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)("text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground absolute top-3.5 right-3 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0", // Increases the hit area of the button on mobile.
        "after:absolute after:-inset-2 md:after:hidden", "group-data-[collapsible=icon]:hidden", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
        lineNumber: 425,
        columnNumber: 5
    }, this);
}
_c12 = SidebarGroupAction;
function SidebarGroupContent({ className, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
        "data-slot": "sidebar-group-content",
        "data-sidebar": "group-content",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)("w-full text-sm", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
        lineNumber: 445,
        columnNumber: 5
    }, this);
}
_c13 = SidebarGroupContent;
function SidebarMenu({ className, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("ul", {
        "data-slot": "sidebar-menu",
        "data-sidebar": "menu",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)("flex w-full min-w-0 flex-col gap-1", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
        lineNumber: 456,
        columnNumber: 5
    }, this);
}
_c14 = SidebarMenu;
function SidebarMenuItem({ className, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("li", {
        "data-slot": "sidebar-menu-item",
        "data-sidebar": "menu-item",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)("group/menu-item relative", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
        lineNumber: 467,
        columnNumber: 5
    }, this);
}
_c15 = SidebarMenuItem;
const sidebarMenuButtonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_12__.cva)("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0", {
    variants: {
        variant: {
            default: "hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",
            outline: "bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"
        },
        size: {
            default: "h-8 text-sm",
            sm: "h-7 text-xs",
            lg: "h-12 text-sm group-data-[collapsible=icon]:p-0!"
        }
    },
    defaultVariants: {
        variant: "default",
        size: "default"
    }
});
function SidebarMenuButton({ asChild = false, isActive = false, variant = "default", size = "default", tooltip, className, ...props }) {
    _s5();
    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_11__.Slot : "button";
    const { isMobile, state } = useSidebar();
    const button = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {
        "data-slot": "sidebar-menu-button",
        "data-sidebar": "menu-button",
        "data-size": size,
        "data-active": isActive,
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(sidebarMenuButtonVariants({
            variant,
            size
        }), className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
        lineNumber: 515,
        columnNumber: 5
    }, this);
    if (!tooltip) {
        return button;
    }
    if (typeof tooltip === "string") {
        tooltip = {
            children: tooltip
        };
    }
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_9__.Tooltip, {
        children: [
            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_9__.TooltipTrigger, {
                asChild: true,
                children: button
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
                lineNumber: 537,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_9__.TooltipContent, {
                side: "right",
                align: "center",
                hidden: state !== "collapsed" || isMobile,
                ...tooltip
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
                lineNumber: 538,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
        lineNumber: 536,
        columnNumber: 5
    }, this);
}
_s5(SidebarMenuButton, "DSCdbs8JtpmKVxCYgM7sPAZNgB0=", false, function() {
    return [
        useSidebar
    ];
});
_c16 = SidebarMenuButton;
function SidebarMenuAction({ className, asChild = false, showOnHover = false, ...props }) {
    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_11__.Slot : "button";
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {
        "data-slot": "sidebar-menu-action",
        "data-sidebar": "menu-action",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)("text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground peer-hover/menu-button:text-sidebar-accent-foreground absolute top-1.5 right-1 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0", // Increases the hit area of the button on mobile.
        "after:absolute after:-inset-2 md:after:hidden", "peer-data-[size=sm]/menu-button:top-1", "peer-data-[size=default]/menu-button:top-1.5", "peer-data-[size=lg]/menu-button:top-2.5", "group-data-[collapsible=icon]:hidden", showOnHover && "peer-data-[active=true]/menu-button:text-sidebar-accent-foreground group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 md:opacity-0", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
        lineNumber: 560,
        columnNumber: 5
    }, this);
}
_c17 = SidebarMenuAction;
function SidebarMenuBadge({ className, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
        "data-slot": "sidebar-menu-badge",
        "data-sidebar": "menu-badge",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)("text-sidebar-foreground pointer-events-none absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums select-none", "peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground", "peer-data-[size=sm]/menu-button:top-1", "peer-data-[size=default]/menu-button:top-1.5", "peer-data-[size=lg]/menu-button:top-2.5", "group-data-[collapsible=icon]:hidden", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
        lineNumber: 585,
        columnNumber: 5
    }, this);
}
_c18 = SidebarMenuBadge;
function SidebarMenuSkeleton({ className, showIcon = false, ...props }) {
    _s6();
    // Random width between 50 to 90%.
    const width = react__WEBPACK_IMPORTED_MODULE_1__.useMemo({
        "SidebarMenuSkeleton.useMemo[width]": ()=>{
            return `${Math.floor(Math.random() * 40) + 50}%`;
        }
    }["SidebarMenuSkeleton.useMemo[width]"], []);
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
        "data-slot": "sidebar-menu-skeleton",
        "data-sidebar": "menu-skeleton",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)("flex h-8 items-center gap-2 rounded-md px-2", className),
        ...props,
        children: [
            showIcon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__.Skeleton, {
                className: "size-4 rounded-md",
                "data-sidebar": "menu-skeleton-icon"
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
                lineNumber: 622,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__.Skeleton, {
                className: "h-4 max-w-(--skeleton-width) flex-1",
                "data-sidebar": "menu-skeleton-text",
                style: {
                    "--skeleton-width": width
                }
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
                lineNumber: 627,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
        lineNumber: 615,
        columnNumber: 5
    }, this);
}
_s6(SidebarMenuSkeleton, "nKFjX4dxbYo91VAj5VdWQ1XUe3I=");
_c19 = SidebarMenuSkeleton;
function SidebarMenuSub({ className, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("ul", {
        "data-slot": "sidebar-menu-sub",
        "data-sidebar": "menu-sub",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)("border-sidebar-border mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l px-2.5 py-0.5", "group-data-[collapsible=icon]:hidden", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
        lineNumber: 642,
        columnNumber: 5
    }, this);
}
_c20 = SidebarMenuSub;
function SidebarMenuSubItem({ className, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("li", {
        "data-slot": "sidebar-menu-sub-item",
        "data-sidebar": "menu-sub-item",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)("group/menu-sub-item relative", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
        lineNumber: 660,
        columnNumber: 5
    }, this);
}
_c21 = SidebarMenuSubItem;
function SidebarMenuSubButton({ asChild = false, size = "md", isActive = false, className, ...props }) {
    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_11__.Slot : "a";
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {
        "data-slot": "sidebar-menu-sub-button",
        "data-sidebar": "menu-sub-button",
        "data-size": size,
        "data-active": isActive,
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)("text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground active:bg-sidebar-accent active:text-sidebar-accent-foreground [&>svg]:text-sidebar-accent-foreground flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 outline-hidden focus-visible:ring-2 disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0", "data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground", size === "sm" && "text-xs", size === "md" && "text-sm", "group-data-[collapsible=icon]:hidden", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sidebar.tsx",
        lineNumber: 683,
        columnNumber: 5
    }, this);
}
_c22 = SidebarMenuSubButton;

Sidebar.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "Sidebar",
    "props": {
        "side": {
            "required": false,
            "tsType": {
                "name": "union",
                "raw": "\"left\" | \"right\"",
                "elements": [
                    {
                        "name": "literal",
                        "value": "\"left\""
                    },
                    {
                        "name": "literal",
                        "value": "\"right\""
                    }
                ]
            },
            "description": "",
            "defaultValue": {
                "value": "\"left\"",
                "computed": false
            }
        },
        "variant": {
            "required": false,
            "tsType": {
                "name": "union",
                "raw": "\"sidebar\" | \"floating\" | \"inset\"",
                "elements": [
                    {
                        "name": "literal",
                        "value": "\"sidebar\""
                    },
                    {
                        "name": "literal",
                        "value": "\"floating\""
                    },
                    {
                        "name": "literal",
                        "value": "\"inset\""
                    }
                ]
            },
            "description": "",
            "defaultValue": {
                "value": "\"sidebar\"",
                "computed": false
            }
        },
        "collapsible": {
            "required": false,
            "tsType": {
                "name": "union",
                "raw": "\"offcanvas\" | \"icon\" | \"none\"",
                "elements": [
                    {
                        "name": "literal",
                        "value": "\"offcanvas\""
                    },
                    {
                        "name": "literal",
                        "value": "\"icon\""
                    },
                    {
                        "name": "literal",
                        "value": "\"none\""
                    }
                ]
            },
            "description": "",
            "defaultValue": {
                "value": "\"offcanvas\"",
                "computed": false
            }
        }
    }
};
SidebarContent.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "SidebarContent"
};
SidebarFooter.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "SidebarFooter"
};
SidebarGroup.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "SidebarGroup"
};
SidebarGroupAction.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "SidebarGroupAction",
    "props": {
        "asChild": {
            "required": false,
            "tsType": {
                "name": "boolean"
            },
            "description": "",
            "defaultValue": {
                "value": "false",
                "computed": false
            }
        }
    }
};
SidebarGroupContent.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "SidebarGroupContent"
};
SidebarGroupLabel.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "SidebarGroupLabel",
    "props": {
        "asChild": {
            "required": false,
            "tsType": {
                "name": "boolean"
            },
            "description": "",
            "defaultValue": {
                "value": "false",
                "computed": false
            }
        }
    }
};
SidebarHeader.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "SidebarHeader"
};
SidebarInput.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "SidebarInput"
};
SidebarInset.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "SidebarInset"
};
SidebarMenu.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "SidebarMenu"
};
SidebarMenuAction.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "SidebarMenuAction",
    "props": {
        "asChild": {
            "required": false,
            "tsType": {
                "name": "boolean"
            },
            "description": "",
            "defaultValue": {
                "value": "false",
                "computed": false
            }
        },
        "showOnHover": {
            "required": false,
            "tsType": {
                "name": "boolean"
            },
            "description": "",
            "defaultValue": {
                "value": "false",
                "computed": false
            }
        }
    }
};
SidebarMenuBadge.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "SidebarMenuBadge"
};
SidebarMenuButton.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "SidebarMenuButton",
    "props": {
        "asChild": {
            "required": false,
            "tsType": {
                "name": "boolean"
            },
            "description": "",
            "defaultValue": {
                "value": "false",
                "computed": false
            }
        },
        "isActive": {
            "required": false,
            "tsType": {
                "name": "boolean"
            },
            "description": "",
            "defaultValue": {
                "value": "false",
                "computed": false
            }
        },
        "tooltip": {
            "required": false,
            "tsType": {
                "name": "union",
                "raw": "string | React.ComponentProps<typeof TooltipContent>",
                "elements": [
                    {
                        "name": "string"
                    },
                    {
                        "name": "ReactComponentProps",
                        "raw": "React.ComponentProps<typeof TooltipContent>",
                        "elements": [
                            {
                                "name": "TooltipContent"
                            }
                        ]
                    }
                ]
            },
            "description": ""
        },
        "variant": {
            "defaultValue": {
                "value": "\"default\"",
                "computed": false
            },
            "required": false
        },
        "size": {
            "defaultValue": {
                "value": "\"default\"",
                "computed": false
            },
            "required": false
        }
    }
};
SidebarMenuItem.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "SidebarMenuItem"
};
SidebarMenuSkeleton.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "SidebarMenuSkeleton",
    "props": {
        "showIcon": {
            "required": false,
            "tsType": {
                "name": "boolean"
            },
            "description": "",
            "defaultValue": {
                "value": "false",
                "computed": false
            }
        }
    }
};
SidebarMenuSub.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "SidebarMenuSub"
};
SidebarMenuSubButton.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "SidebarMenuSubButton",
    "props": {
        "asChild": {
            "required": false,
            "tsType": {
                "name": "boolean"
            },
            "description": "",
            "defaultValue": {
                "value": "false",
                "computed": false
            }
        },
        "size": {
            "required": false,
            "tsType": {
                "name": "union",
                "raw": "\"sm\" | \"md\"",
                "elements": [
                    {
                        "name": "literal",
                        "value": "\"sm\""
                    },
                    {
                        "name": "literal",
                        "value": "\"md\""
                    }
                ]
            },
            "description": "",
            "defaultValue": {
                "value": "\"md\"",
                "computed": false
            }
        },
        "isActive": {
            "required": false,
            "tsType": {
                "name": "boolean"
            },
            "description": "",
            "defaultValue": {
                "value": "false",
                "computed": false
            }
        }
    }
};
SidebarMenuSubItem.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "SidebarMenuSubItem"
};
SidebarProvider.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "SidebarProvider",
    "props": {
        "defaultOpen": {
            "required": false,
            "tsType": {
                "name": "boolean"
            },
            "description": "",
            "defaultValue": {
                "value": "true",
                "computed": false
            }
        },
        "open": {
            "required": false,
            "tsType": {
                "name": "boolean"
            },
            "description": ""
        },
        "onOpenChange": {
            "required": false,
            "tsType": {
                "name": "signature",
                "type": "function",
                "raw": "(open: boolean) => void",
                "signature": {
                    "arguments": [
                        {
                            "type": {
                                "name": "boolean"
                            },
                            "name": "open"
                        }
                    ],
                    "return": {
                        "name": "void"
                    }
                }
            },
            "description": ""
        }
    }
};
SidebarRail.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "SidebarRail"
};
SidebarSeparator.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "SidebarSeparator"
};
SidebarTrigger.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "SidebarTrigger"
};
var _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19, _c20, _c21, _c22;
__webpack_require__.$Refresh$.register(_c, "SidebarProvider");
__webpack_require__.$Refresh$.register(_c1, "Sidebar");
__webpack_require__.$Refresh$.register(_c2, "SidebarTrigger");
__webpack_require__.$Refresh$.register(_c3, "SidebarRail");
__webpack_require__.$Refresh$.register(_c4, "SidebarInset");
__webpack_require__.$Refresh$.register(_c5, "SidebarInput");
__webpack_require__.$Refresh$.register(_c6, "SidebarHeader");
__webpack_require__.$Refresh$.register(_c7, "SidebarFooter");
__webpack_require__.$Refresh$.register(_c8, "SidebarSeparator");
__webpack_require__.$Refresh$.register(_c9, "SidebarContent");
__webpack_require__.$Refresh$.register(_c10, "SidebarGroup");
__webpack_require__.$Refresh$.register(_c11, "SidebarGroupLabel");
__webpack_require__.$Refresh$.register(_c12, "SidebarGroupAction");
__webpack_require__.$Refresh$.register(_c13, "SidebarGroupContent");
__webpack_require__.$Refresh$.register(_c14, "SidebarMenu");
__webpack_require__.$Refresh$.register(_c15, "SidebarMenuItem");
__webpack_require__.$Refresh$.register(_c16, "SidebarMenuButton");
__webpack_require__.$Refresh$.register(_c17, "SidebarMenuAction");
__webpack_require__.$Refresh$.register(_c18, "SidebarMenuBadge");
__webpack_require__.$Refresh$.register(_c19, "SidebarMenuSkeleton");
__webpack_require__.$Refresh$.register(_c20, "SidebarMenuSub");
__webpack_require__.$Refresh$.register(_c21, "SidebarMenuSubItem");
__webpack_require__.$Refresh$.register(_c22, "SidebarMenuSubButton");


const $ReactRefreshModuleId$ = __webpack_require__.$Refresh$.moduleId;
const $ReactRefreshCurrentExports$ = __react_refresh_utils__.getModuleExports(
	$ReactRefreshModuleId$
);

function $ReactRefreshModuleRuntime$(exports) {
	if (true) {
		let errorOverlay;
		if (true) {
			errorOverlay = false;
		}
		let testMode;
		if (typeof __react_refresh_test__ !== 'undefined') {
			testMode = __react_refresh_test__;
		}
		return __react_refresh_utils__.executeRuntime(
			exports,
			$ReactRefreshModuleId$,
			module.hot,
			errorOverlay,
			testMode
		);
	}
}

if (typeof Promise !== 'undefined' && $ReactRefreshCurrentExports$ instanceof Promise) {
	$ReactRefreshCurrentExports$.then($ReactRefreshModuleRuntime$);
} else {
	$ReactRefreshModuleRuntime$($ReactRefreshCurrentExports$);
}

/***/ })

});
//# sourceMappingURL=sidebar-stories.4f6bbce0c7347410d4ab.hot-update.js.map